<?php
require __DIR__ . '/../vendor/autoload.php';

use Twilio\Rest\Client;

if (isset($_POST['send_sms'])) {
    $receiverNumber = htmlspecialchars($_POST['phone']);
    $smsMessage = htmlspecialchars($_POST['message']);

    // Twilio credentials
    $sid    = "your_account_sid";   // Twilio Account SID
    $token  = "your_auth_token";    // Twilio Auth Token
    $twilioNumber = "+**********";  // Your Twilio phone number

    try {
        $twilio = new Twilio\Rest\Client($sid, $token);

        $message = $twilio->messages->create(
            $receiver = $toNumber = $toNumber,
            [
                "body" => $smsContent,
                "from" => $twilioNumber
            ]
        );

        $status = "success";
        $response = "SMS sent successfully!";
    } catch (Exception $e) {
        $status = "error";
        $response = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SMS Sender using Twilio</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto|Courgette|Pacifico:400,700">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>    
    body {
        color: #000;
        background: #2ec4fc;
        font-family: "Roboto", sans-serif;
    }
    .sms-form {
        padding: 50px;
        margin: 30px auto;
    }	
    .sms-form h1 {
        font-size: 42px;
        font-family: 'Pacifico', sans-serif;
        margin-bottom: 50px;
        text-align: center;
    }
    .sms-form .form-control, .sms-form .btn {
        min-height: 40px;
        border-radius: 2px;
    }
    .sms-form .form-control {
        border-color: #03a9f4;
    }
    .sms-form .form-control:focus {
        border-color: #039be5;
        box-shadow: 0 0 8px #039be5;
    }
    .sms-form .btn-primary {
        min-width: 250px;
        color: #fff;
        background: #039be5 !important;
        margin-top: 20px;
        border: none;
    }
    .sms-form textarea {
        resize: vertical;
    }
    </style>
</head>
<body>
<div class="container-lg">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="sms-form">
                <h1>Send SMS</h1>
                <form method="post">
                    <div class="form-group">
                        <label for="phoneNumber">Phone Number</label>
                        <input type="text" class="form-control" id="phoneNumber" name="toemail" placeholder="+255XXXXXXXXX" required>
                    </div>

                    <div class="form-group">
                        <label for="inputMessage">Message</label>
                        <textarea class="form-control" id="inputMessage" name="message" rows="5" required></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary" name="send">
                            <i class="fa fa-send"></i> Send SMS
                        </button>
                    </div>            
                </form>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
<?php if(isset($status)): ?>
    Swal.fire({
        icon: '<?= $status ?>',
        title: '<?= ($status == "success") ? "Success!" : "Oops!"; ?>',
        text: '<?= $response ?>',
        confirmButtonColor: '#3085d6',
    });
<?php endif; ?>
</script>

</body>
</html>
