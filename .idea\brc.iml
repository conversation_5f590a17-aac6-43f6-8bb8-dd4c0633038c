<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laminas/laminas-diactoros" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/stella-maris/clock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/twilio/sdk" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vonage/client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vonage/client-core" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vonage/nexmo-bridge" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>