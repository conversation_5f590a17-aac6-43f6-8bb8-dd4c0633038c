<?php
include '../includes/db.php';
include '../includes/header.php';

$matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                         FROM matches 
                         JOIN leagues ON matches.league_id = leagues.id 
                         JOIN teams t1 ON matches.home_team_id = t1.id 
                         JOIN teams t2 ON matches.away_team_id = t2.id");
?>

<h1>View Matches</h1>
<table class="table table-bordered">
    <thead>
        <tr>
            <th>ID</th>
            <th>League</th>
            <th>Home Team</th>
            <th>Away Team</th>
            <th>Date</th>
            <th>Status</th>
            <th>Score</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($row = $matches->fetch_assoc()) { ?>
        <tr>
            <td><?php echo $row['id']; ?></td>
            <td><?php echo $row['league_name']; ?></td>
            <td><?php echo $row['home_team']; ?></td>
            <td><?php echo $row['away_team']; ?></td>
            <td><?php echo $row['date']; ?></td>
            <td><?php echo ucfirst($row['status']); ?></td>
            <td><?php echo $row['home_team_score']; ?> - <?php echo $row['away_team_score']; ?></td>
        </tr>
        <?php } ?>
    </tbody>
</table>



<?php include '../includes/footer.php'; ?>