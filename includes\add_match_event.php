<?php
include '../includes/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $match_id = $_POST['match_id'];
    $event_type = $_POST['event_type'];
    $player_id = $_POST['player_id'];
    $team_id = $_POST['team_id'];
    $minute = $_POST['minute'];

    // Insert event into database
    $sql = "INSERT INTO match_events (match_id, event_type, player_id, team_id, minute)
            VALUES ('$match_id', '$event_type', '$player_id', '$team_id', '$minute')";

    if ($conn->query($sql)) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => $conn->error]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>