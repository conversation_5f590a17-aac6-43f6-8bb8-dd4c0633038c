<?php
// admin_overall_stats.php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Create overall_stats table if it does not exist


// Calculate overall team stats from all finished matches
$teams_query = "SELECT id, name FROM teams";
$teams_result = $conn->query($teams_query);

if ($teams_result && $teams_result->num_rows > 0) {
    while ($team = $teams_result->fetch_assoc()) {
        $team_id = $team['id'];
        // Initialize stats
        $played = 0;
        $won = 0;
        $drawn = 0;
        $lost = 0;
        $goals_for = 0;
        $goals_against = 0;
        
        // Fetch finished matches where this team participated (either home or away)
        $matches_query = "SELECT * FROM matches 
                          WHERE status = 'finished' 
                          AND (home_team_id = $team_id OR away_team_id = $team_id)";
        $matches_result = $conn->query($matches_query);
        if ($matches_result && $matches_result->num_rows > 0) {
            while ($match = $matches_result->fetch_assoc()) {
                $played++;
                if ($match['home_team_id'] == $team_id) {
                    $goals_for += $match['home_team_score'];
                    $goals_against += $match['away_team_score'];
                    if ($match['home_team_score'] > $match['away_team_score']) {
                        $won++;
                    } elseif ($match['home_team_score'] == $match['away_team_score']) {
                        $drawn++;
                    } else {
                        $lost++;
                    }
                } else {
                    $goals_for += $match['away_team_score'];
                    $goals_against += $match['home_team_score'];
                    if ($match['away_team_score'] > $match['home_team_score']) {
                        $won++;
                    } elseif ($match['away_team_score'] == $match['home_team_score']) {
                        $drawn++;
                    } else {
                        $lost++;
                    }
                }
            }
        }
        $goal_difference = $goals_for - $goals_against;
        
        // Store overall stats in the table using REPLACE (insert new or update existing)
        $stmt = $conn->prepare("REPLACE INTO overall_stats (team_id, played, won, drawn, lost, goals_for, goals_against, goal_difference) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("iiiiiiii", $team_id, $played, $won, $drawn, $lost, $goals_for, $goals_against, $goal_difference);
        $stmt->execute();
        $stmt->close();
    }
}

// Fetch overall stats (joined with team info) for display
$stats_query = "SELECT os.*, t.name as team_name, t.logo as team_logo FROM overall_stats os JOIN teams t ON os.team_id = t.id ORDER BY t.name ASC";
$stats_result = $conn->query($stats_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Admin - Overall Team Stats</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    /* Figma-inspired minimalist design */
    body {
      font-family: 'Helvetica Neue', sans-serif;
      background-color: #f0f2f5;
      color: #333;
      padding: 20px;
    }
    .container {
      margin-top: 30px;
    }
    .stats-table th, .stats-table td {
      text-align: left;
      vertical-align: middle;
    }
    .team-logo {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 50%;
      margin-right: 8px;
    }
    .table-header {
      background-color: #343a40;
      color: #fff;
      text-transform: uppercase;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="mb-4">Admin: Overall Team Stats (All Matches Played)</h1>
    <table class="table table-bordered stats-table">
      <thead class="table-header">
        <tr>
          <th>Team</th>
          <th>Played</th>
          <th>Won</th>
          <th>Drawn</th>
          <th>Lost</th>
          <th>GF</th>
          <th>GA</th>
          <th>GD</th>
        </tr>
      </thead>
      <tbody>
        <?php if ($stats_result && $stats_result->num_rows > 0): ?>
          <?php while ($row = $stats_result->fetch_assoc()): ?>
            <tr>
              <td>
                <?php if (!empty($row['team_logo'])): ?>
                  <img src="uploads/<?php echo htmlspecialchars($row['team_logo']); ?>" alt="Logo" class="team-logo">
                <?php endif; ?>
                <?php echo htmlspecialchars($row['team_name']); ?>
              </td>
              <td><?php echo $row['played']; ?></td>
              <td><?php echo $row['won']; ?></td>
              <td><?php echo $row['drawn']; ?></td>
              <td><?php echo $row['lost']; ?></td>
              <td><?php echo $row['goals_for']; ?></td>
              <td><?php echo $row['goals_against']; ?></td>
              <td><?php echo $row['goal_difference']; ?></td>
            </tr>
          <?php endwhile; ?>
        <?php else: ?>
          <tr><td colspan="8">No data available.</td></tr>
        <?php endif; ?>
      </tbody>
    </table>
  </div>
</body>
</html>

<?php
$conn->close();
?>
