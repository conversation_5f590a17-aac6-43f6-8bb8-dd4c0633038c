<?php
include '../includes/db.php';
include 'check_login.php';
//include '../includes/header.php';
include './includes/header.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_group'])) {
    $name = $_POST['name'];

    $sql = "INSERT INTO `groups` (name) VALUES ('$name')";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Group created successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Group</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Create Group</h1>
        <form method="POST">
            <div class="mb-3">
                <label class="form-label">Group Name</label>
                <input type="text" name="name" class="form-control" required>
            </div>
            <button type="submit" name="create_group" class="btn btn-primary">Create Group</button>
        </form>
    </div>
</body>
</html>