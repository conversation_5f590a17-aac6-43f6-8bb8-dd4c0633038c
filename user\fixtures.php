<?php
include '../includes/db.php';
include '../includes/header.php';

$fixtures = $conn->query("SELECT fixtures.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                          FROM fixtures 
                          JOIN leagues ON fixtures.league_id = leagues.id 
                          JOIN matches ON fixtures.match_id = matches.id 
                          JOIN teams t1 ON matches.home_team_id = t1.id 
                          JOIN teams t2 ON matches.away_team_id = t2.id");
?>

<h1>View Fixtures</h1>
<table class="table table-bordered">
    <thead>
        <tr>
            <th>ID</th>
            <th>League</th>
            <th>Match</th>
            <th>Round</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($row = $fixtures->fetch_assoc()) { ?>
        <tr>
            <td><?php echo $row['id']; ?></td>
            <td><?php echo $row['league_name']; ?></td>
            <td><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?></td>
            <td><?php echo $row['round']; ?></td>
        </tr>
        <?php } ?>
    </tbody>
</table>

<?php include '../includes/footer.php'; ?>