<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Create League
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_league'])) {
    $name = $_POST['name'];
    $country = $_POST['country'];
    $season = $_POST['season'];

    $sql = "INSERT INTO leagues (name, country, season) VALUES ('$name', '$country', '$season')";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>League created successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Delete League
if (isset($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];
    $sql = "DELETE FROM leagues WHERE id = $delete_id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>League deleted successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Update League
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_league'])) {
    $id = $_POST['id'];
    $name = $_POST['name'];
    $country = $_POST['country'];
    $season = $_POST['season'];

    $sql = "UPDATE leagues SET name = '$name', country = '$country', season = '$season' WHERE id = $id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>League updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Fetch Leagues
$leagues = $conn->query("SELECT * FROM leagues");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Leagues</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>Manage Leagues</h1>
        <form method="POST" class="mb-4">
            <div class="mb-3">
                <input type="text" name="name" placeholder="League Name" class="form-control" required>
            </div>
            <div class="mb-3">
                <input type="text" name="country" placeholder="Country" class="form-control" required>
            </div>
            <div class="mb-3">
                <input type="text" name="season" placeholder="Season" class="form-control" required>
            </div>
            <button type="submit" name="create_league" class="btn btn-primary">Create League</button>
        </form>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Country</th>
                    <th>Season</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $leagues->fetch_assoc()) { ?>
                <tr>
                    <td><?php echo $row['id']; ?></td>
                    <td><?php echo $row['name']; ?></td>
                    <td><?php echo $row['country']; ?></td>
                    <td><?php echo $row['season']; ?></td>
                    <td>
                        <!-- Edit Button -->
                        <button class="btn btn-warning btn-sm edit-league" data-id="<?php echo $row['id']; ?>" data-name="<?php echo $row['name']; ?>" data-country="<?php echo $row['country']; ?>" data-season="<?php echo $row['season']; ?>">Edit</button>
                        <!-- Delete Button -->
                        <a href="?delete_id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this league?')">Delete</a>
                    </td>
                </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>

    <!-- Edit League Modal -->
    <div class="modal fade" id="editLeagueModal" tabindex="-1" aria-labelledby="editLeagueModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editLeagueModalLabel">Edit League</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editLeagueForm" method="POST">
                        <input type="hidden" name="id" id="editLeagueId">
                        <div class="mb-3">
                            <label for="editLeagueName" class="form-label">League Name</label>
                            <input type="text" name="name" id="editLeagueName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label for="editLeagueCountry" class="form-label">Country</label>
                            <input type="text" name="country" id="editLeagueCountry" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label for="editLeagueSeason" class="form-label">Season</label>
                            <input type="text" name="season" id="editLeagueSeason" class="form-control" required>
                        </div>
                        <button type="submit" name="update_league" class="btn btn-primary">Update League</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>

    <script>
        // JavaScript to handle the edit modal
        $(document).ready(function() {
            $('.edit-league').on('click', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');
                var country = $(this).data('country');
                var season = $(this).data('season');

                $('#editLeagueId').val(id);
                $('#editLeagueName').val(name);
                $('#editLeagueCountry').val(country);
                $('#editLeagueSeason').val(season);

                $('#editLeagueModal').modal('show');
            });
        });
    </script>
</body>
</html>

<?php include '../includes/footer.php'; ?>