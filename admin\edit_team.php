<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';
if (!isset($_GET['id'])) {
    header("Location: teams.php");
    exit();
}

$id = $_GET['id'];

// Fetch Team Details
$team = $conn->query("SELECT * FROM teams WHERE id = $id")->fetch_assoc();
$leagues = $conn->query("SELECT * FROM leagues");

// Update Team
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_team'])) {
    $name = $_POST['name'];
    $league_id = $_POST['league_id'];
    $description = $_POST['description']; // Get the description from the form

    // Handle logo update
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $targetDir = "uploads/";
        $targetFile = $targetDir. basename($_FILES["logo"]["name"]);
        $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
        $uploadOk = 1;

        //... (Image validation - same as in create team logic)...

        if ($uploadOk == 0) {
            echo "<div class='alert alert-danger'>Sorry, your file was not uploaded.</div>";
        } else {
            if (move_uploaded_file($_FILES["logo"]["tmp_name"], $targetFile)) {
                $logoName = basename($_FILES["logo"]["name"]);

                // Delete the old logo if it exists
                if (!empty($team['logo']) && file_exists($targetDir. $team['logo'])) {
                    unlink($targetDir. $team['logo']);
                }

                // Update the logo name in the database
                $sql = "UPDATE teams SET logo = '$logoName' WHERE id = $id";
                if (!$conn->query($sql)) {
                    echo "<div class='alert alert-danger'>Error updating logo: ". $conn->error. "</div>";
                }
            } else {
                echo "<div class='alert alert-danger'>Sorry, there was an error uploading your file.</div>";
            }
        }
    }

    $sql = "UPDATE teams SET name = '$name', league_id = '$league_id', description = '$description' WHERE id = $id";
    if ($conn->query($sql) === TRUE) {
        echo "<div class='alert alert-success'>Team updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: ". $sql. "<br>". $conn->error. "</div>";
    }
}?>

<h1>Edit Team</h1>
<div class="card">  
    <div class="card-body"> 
        <form method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <input type="text" name="name" placeholder="Team Name" class="form-control" value="<?php echo $team['name'];?>" required>
            </div>
            <div class="mb-3">
                <select name="league_id" class="form-control" required>
                    <option value="">Select League</option>
                    <?php while ($row = $leagues->fetch_assoc()) {?>
                    <option value="<?php echo $row['id'];?>" <?php if ($row['id'] == $team['league_id']) echo 'selected';?>><?php echo $row['name'];?></option>
                    <?php }?>
                </select>
            </div>
            <div class="mb-3">
                <label for="logo" class="form-label">Logo</label>
                <input type="file" name="logo" id="logo" class="form-control" accept="image/*">
                <?php if (!empty($team['logo'])) {?>
                    <img src="uploads/<?php echo $team['logo'];?>" alt="Team Logo" width="50">
                <?php }?>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea name="description" id="description" class="form-control" rows="5"><?php echo $team['description'];?></textarea>
            </div>
            <button type="submit" name="update_team" class="btn btn-primary">Update Team</button>
        </form>

        <a href="teams.php" class="btn btn-secondary mt-3">Back to Teams</a> <div class="mt-3"></div>
    </div> 
</div>

<?php include '../includes/footer.php';?>