<?php
require_once 'db.php';
header('Content-Type: application/json');

// Validate CSRF token
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    http_response_code(403);
    echo json_encode(['error' => 'Invalid CSRF token']);
    exit;
}

// Validate input
$required = ['email', 'amount', 'tx_ref', 'phone'];
foreach ($required as $field) {
    if (empty($_POST[$field])) {
        http_response_code(400);
        echo json_encode(['error' => "Missing required field: $field"]);
        exit;
    }
}

// Prepare payment data
$paymentData = [
    'tx_ref' => $_POST['tx_ref'],
    'amount' => $_POST['amount'],
    'currency' => 'NGN',
    'payment_options' => 'card,ussd,banktransfer',
    'redirect_url' => BASE_URL . '/verify_payment.php',
    'customer' => [
        'email' => $_POST['email'],
        'phonenumber' => $_POST['phone'],
        'name' => $_POST['name'] ?? 'Customer'
    ],
    'customizations' => [
        'title' => 'Your Store Name',
        'description' => 'Payment for items in cart',
        'logo' => BASE_URL . '/logo.png'
    ]
];

// Initialize cURL request to Flutterwave
$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, 'https://api.flutterwave.com/v3/payments');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . FLW_SECRET_KEY,
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    http_response_code(400);
    echo json_encode(['error' => 'Failed to initialize payment']);
    exit;
}

$responseData = json_decode($response, true);

// Store transaction in database
try {
    $stmt = $db->prepare("INSERT INTO transactions 
        (tx_ref, amount, currency, customer_email, customer_phone, status, created_at)
        VALUES (?, ?, ?, ?, ?, 'initiated', NOW())");
    $stmt->execute([
        $paymentData['tx_ref'],
        $paymentData['amount'],
        $paymentData['currency'],
        $paymentData['customer']['email'],
        $paymentData['customer']['phonenumber']
    ]);
} catch(PDOException $e) {
    // Log error but don't fail the payment
    error_log("Database error: " . $e->getMessage());
}

echo json_encode([
    'status' => 'success',
    'payment_link' => $responseData['data']['link']
]);
?>