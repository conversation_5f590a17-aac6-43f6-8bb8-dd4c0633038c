<?php
include '../includes/db.php';
include 'check_login.php';

// Decode the JSON input
$data = json_decode(file_get_contents('php://input'), true);

// Extract data from the input
$matchId = $data['match_id'];
$eventType = $data['event_type'];
$teamId = $data['team_id'];
$playerId = $data['player_id'];  // Can be null
$minute = $data['minute'];

// Basic input validation (IMPORTANT)
if (!is_numeric($matchId) || !is_numeric($teamId) || !is_numeric($minute)) {
    echo json_encode(['success' => false, 'message' => 'Invalid input.']);
    exit;
}

// Validate event type
$allowedEventTypes = ['goal', 'assist', 'yellow_card', 'red_card', 'penalty', 'clean_sheet', 'first_half', 'half_time', 'second_half'];
if (!in_array($eventType, $allowedEventTypes)) {
    echo json_encode(['success' => false, 'message' => 'Invalid event type.']);
    exit;
}

// Prepare the SQL statement
$stmt = $conn->prepare("INSERT INTO match_events (match_id, event_type, team_id, player_id, minute) VALUES (?, ?, ?, ?, ?)");

// Handle NULL for player_id
if ($playerId === '') {
    $playerId = null; // Set to NULL if empty string
}

// Bind parameters
$stmt->bind_param("isiii", $matchId, $eventType, $teamId, $playerId, $minute);

// Execute the statement
if ($stmt->execute()) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => $stmt->error]);
}

// Close the statement and connection
$stmt->close();
$conn->close();
?>