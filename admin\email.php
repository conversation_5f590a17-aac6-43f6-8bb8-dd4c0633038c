<?php
require __DIR__ . '/../vendor/autoload.php';

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

if (isset($_POST['send'])) {
    $fname   = htmlspecialchars($_POST['fname']);
    $toemail = $_POST['toemail'];
    $subject = htmlspecialchars($_POST['subject']);
    $message = htmlspecialchars($_POST['message']);

    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>'; 
        $mail->Password   = 'qaqo lsxw gubs ckhp';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;

        $mail->setFrom('<EMAIL>', 'CHAZ');
        $mail->addReplyTo('<EMAIL>', 'CHAZ');
        $mail->addAddress($toemail);

        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body    = 'Dear ' . $fname . '<p>' . nl2br($message) . '</p>';

        $mail->send();
        $status = "success";
        $response = "Your message has been sent successfully!";
    } catch (Exception $e) {
        $status = "error";
        $response = "Mailer Error: {$mail->ErrorInfo}";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Email Sender with PHPMailer</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto|Courgette|Pacifico:400,700">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>    
    body {
        color: #000;
        background: #fcda2e;
        font-family: "Roboto", sans-serif;
    }
    .contact-form {
        padding: 50px;
        margin: 30px auto;
    }	
    .contact-form h1 {
        font-size: 42px;
        font-family: 'Pacifico', sans-serif;
        margin-bottom: 50px;
        text-align: center;
    }
    .contact-form .form-control, .contact-form .btn {
        min-height: 40px;
        border-radius: 2px;
    }
    .contact-form .form-control {
        border-color: #e2c705;
    }
    .contact-form .form-control:focus {
        border-color: #d8b012;
        box-shadow: 0 0 8px #dcae10;
    }
    .contact-form .btn-primary {
        min-width: 250px;
        color: #fcda2e;
        background: #000 !important;
        margin-top: 20px;
        border: none;
    }
    .contact-form textarea {
        resize: vertical;
    }
    </style>
</head>
<body>
<div class="container-lg">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="contact-form">
                <h1>Get in Touch</h1>
                <form method="post" action="">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="inputName">Name</label>
                                <input type="text" class="form-control" id="inputName" name="fname" required>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label for="inputEmail">Email</label>
                                <input type="email" class="form-control" id="inputEmail" name="toemail" required>
                            </div>
                        </div>
                    </div>            

                    <div class="form-group">
                        <label for="inputSubject">Subject</label>
                        <input type="text" class="form-control" id="inputSubject" name="subject" required>
                    </div>

                    <div class="form-group">
                        <label for="inputMessage">Message</label>
                        <textarea class="form-control" id="inputMessage" name="message" rows="5" required></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary" name="send">
                            <i class="fa fa-paper-plane"></i> Send
                        </button>
                    </div>            
                </form>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert popup notifications -->
<script>
<?php if(isset($status)): ?>
    Swal.fire({
        icon: '<?= $status; ?>',
        title: '<?= ($status == "success") ? "Success!" : "Oops!"; ?>',
        text: '<?= $response; ?>',
        confirmButtonColor: '#000',
    });
<?php endif; ?>
</script>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"></script>

</body>
</html>
