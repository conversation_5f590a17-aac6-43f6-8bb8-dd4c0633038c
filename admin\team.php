<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';
if (!isset($_GET['id'])) {
    header("Location: teams.php");
    exit();
}

$team_id = $_GET['id'];

// Fetch Team Details
$team = $conn->query("SELECT teams.*, leagues.name as league_name 
                        FROM teams 
                        JOIN leagues ON teams.league_id = leagues.id 
                        WHERE teams.id = $team_id")->fetch_assoc();

// Fetch Squad (Players) with image and position
$squad = $conn->query("SELECT * FROM players WHERE team_id = $team_id");




// Fetch Next Match - Adjust SQL as needed
$next_match = $conn->query("SELECT matches.*, t1.name AS home_team, t2.name AS away_team
                            FROM matches
                            JOIN teams t1 ON matches.home_team_id = t1.id
                            JOIN teams t2 ON matches.away_team_id = t2.id
                            WHERE (home_team_id = $team_id OR away_team_id = t2.id)
                            AND date > NOW() 
                            ORDER BY date ASC LIMIT 1")->fetch_assoc();?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Team Info</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="shortcut icon" href="" type="image/x-icon" />
    <link rel="apple-touch-icon" href="">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="css/colors.css">
    <link rel="stylesheet" href="css/versions.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/custom.css">
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/3dslider.css" />
    <script src="http://code.jquery.com/jquery-1.11.1.min.js"></script>
    <script src="js/3dslider.js"></script>
    <style>
      .card.img-responsive {
            height: 200px;
            object-fit: cover;
        }
        <style>
      .card.img-responsive {
            height: 200px;
            object-fit: cover;
        }
        /* CSS to make the cards responsive */
        @media (max-width: 768px) { 
          .col-md-3 {
                flex: 0 0 50%; 
                max-width: 50%;
            }
        }
        @media (max-width: 576px) { 
          .col-md-3 {
                flex: 0 0 100%; 
                max-width: 100%;
            }
        }
         .footer-col, .footer-col * { /* Apply to footer-col and all its children */
  color: white !important;
}

    </style>
    </style>
</head>
<body class="game_info">
    <div id="preloader">
        <img class="preloader" src="admin/uploads/loading-img.gif" alt="">
    </div>
    <section id="top">
        <div class="inner-information-text">
            <div class="container">
                <h3>Our Team</h3>
                <ul class="breadcrumb">
                    <li><a href="index.php">Home</a></li>
                    <li class="active">Our Team</li>
                </ul>
            </div>
        </div>
        <section id="contant" class="contant main-heading team">
      <div class="row">
         <div class="container">
            <!-- Team Details -->
            <div class="col-md-12">
               <div class="team-details">
                  <img src="<?php echo $team['logo']; ?>" alt="Team Logo" class="team-logo">
                  <h2><?php echo $team['name']; ?></h2>
                  <p><strong>League:</strong> <?php echo $team['league_name']; ?></p>
                  <p><strong>Description:</strong> <?php echo $team['description'] ?? "No description available"; ?></p>
               </div>
            </div>
    </section>
    <section id="contant" class="contant main-heading team">
        <div class="row">
            <div class="container">
                <div class="row">
                    <?php while ($player = $squad->fetch_assoc()) {?>
                        <div class="col-md-3 column">
                            <div class="card">
                                <img class="img-responsive" src="uploads/<?php echo $player['image'];?>" alt="<?php echo $player['name'];?>" style="width:100%">
                                <div class="">
                                    <h4><?php echo $player['name'];?></h4>
                                    <p class="title"><?php echo $player['position'];?></p>
                                    <div class="center"><button class="button">Contact</button></div>
                                </div>
                            </div>
                        </div>
                    <?php }?>
                </div>
            </div>
        </div>
    </section>
          

            <!-- Next Match -->
            <div class="col-md-12">
               <h2>Next Match</h2>
               <?php if ($next_match) { ?>
               <p>
                  Next match on <?php echo date('d M Y H:i', strtotime($next_match['date'])); ?> against 
                  <?php echo ($next_match['home_team_id'] == $team_id ? "Away Team" : "Home Team"); ?>
               </p>
               <?php } else { ?>
               <p>No upcoming matches.</p>
               <?php } ?>
            </div>
         </div>
      </div>
   </section>
   
   
    <a href="#home" data-scroll class="dmtop global-radius"><i class="fa fa-angle-up"></i></a>
    <script src="../assets/js/all.js"></script>
    <script src="../assets/js/custom.js"></script>
</body>
</html>
<?php include '../includes/footer.php'; ?>