    /*
    Flaticon icon font: Flaticon
    Creation date: 03/06/2019 10:47
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-muscle:before { content: "\f100"; }
.flaticon-stationary-bike:before { content: "\f101"; }
.flaticon-banana:before { content: "\f102"; }
.flaticon-heart:before { content: "\f103"; }
.flaticon-scale:before { content: "\f104"; }
.flaticon-weight:before { content: "\f105"; }
    
    $font-Flaticon-muscle: "\f100";
    $font-Flaticon-stationary-bike: "\f101";
    $font-Flaticon-banana: "\f102";
    $font-Flaticon-heart: "\f103";
    $font-Flaticon-scale: "\f104";
    $font-Flaticon-weight: "\f105";