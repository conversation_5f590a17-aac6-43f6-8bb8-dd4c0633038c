<?php
include '../includes/db.php'; // Your database connection

// Get the search query (and sanitize it!)
$search_query = ""; //Initialize
if (isset($_GET['query'])) {
    $search_query = mysqli_real_escape_string($conn, $_GET['query']);  // Sanitize for security
}

// Build the SQL query (using a prepared statement)
$sql = "SELECT * FROM your_table WHERE your_column LIKE ?"; //VERY IMPORTANT, CHANGE THIS
$stmt = $conn->prepare($sql);

if ($stmt) {
    $search_param = "%" . $search_query . "%"; // Add wildcards for partial matching
    $stmt->bind_param("s", $search_param);
    $stmt->execute();
    $result = $stmt->get_result();
  } else {
     echo "Error preparing statement";
  }

?>

<!DOCTYPE html>
<html>
<head>
    <title>Search Results</title>
    </head>
<body>
    <h1>Search Results for "<?php echo htmlspecialchars($search_query); // Display search term, prevent XSS ?>"</h1>

    <?php
    if ($result && $result->num_rows > 0) {
        // Display search results
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            // Display each result item (adjust this to your needs)
            echo "<li>" . htmlspecialchars($row['your_column']) . "</li>"; //CHANGE THIS
        }
        echo "</ul>";
    } else {
        echo "<p>No results found for \"". htmlspecialchars($search_query) ."\".</p>";
    }
   if ($stmt) {
       $stmt->close(); // Close the statement
   }
    $conn->close();
    ?>
</body>
</html>