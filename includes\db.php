<?php

define('FLW_SECRET_KEY', '32193bba8dab84e3d9c4525c85ea7a12-X');
define('FLW_PUBLIC_KEY', '32193bba8dab84e3d9c4525c85ea7a12-X');
define('FLW_ENCRYPTION_KEY', '32193bba8dab84e3d9c4525c85ea7a12-X');
define('BASE_URL', 'https://yourdomain.com'); // Your website's base URL

// Database configuration (for storing transactions)
define('DB_HOST', 'localhost');
define('DB_USER', 'your_db_user');
define('DB_PASS', 'your_db_password');
define('DB_NAME', 'your_db_name');

$host = 'localhost';
$db = 'barberia_2025';
$user = 'root';
$pass = '';

$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Helper function to generate transaction references
function generateTransactionRef() {
    return 'FLW-' . time() . '-' . bin2hex(random_bytes(4));
}
?>