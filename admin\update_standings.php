<?php
include '../includes/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_standings'])) {
    $match_id = $_POST['match_id'];
    $home_score = $_POST['home_team_score'];
    $away_score = $_POST['away_team_score'];

    // Fetch match details
    $match = $conn->query("SELECT * FROM matches WHERE id = $match_id")->fetch_assoc();
    $home_team_id = $match['home_team_id'];
    $away_team_id = $match['away_team_id'];

    // Fetch group IDs for the teams
    $home_group = $conn->query("SELECT group_id FROM group_teams WHERE team_id = $home_team_id")->fetch_assoc();
    $away_group = $conn->query("SELECT group_id FROM group_teams WHERE team_id = $away_team_id")->fetch_assoc();

    if ($home_group && $away_group) {
        $group_id = $home_group['group_id'];

        // Update standings for home team
        $home_standings = $conn->query("SELECT * FROM standings WHERE group_id = $group_id AND team_id = $home_team_id")->fetch_assoc();
        if (!$home_standings) {
            $conn->query("INSERT INTO standings (group_id, team_id) VALUES ('$group_id', '$home_team_id')");
            $home_standings = $conn->query("SELECT * FROM standings WHERE group_id = $group_id AND team_id = $home_team_id")->fetch_assoc();
        }

        // Update standings for away team
        $away_standings = $conn->query("SELECT * FROM standings WHERE group_id = $group_id AND team_id = $away_team_id")->fetch_assoc();
        if (!$away_standings) {
            $conn->query("INSERT INTO standings (group_id, team_id) VALUES ('$group_id', '$away_team_id')");
            $away_standings = $conn->query("SELECT * FROM standings WHERE group_id = $group_id AND team_id = $away_team_id")->fetch_assoc();
        }

        // Update points, played, won, drawn, lost, goals_for, goals_against
        $home_points = $home_standings['points'];
        $home_played = $home_standings['played'] + 1;
        $home_won = $home_standings['won'];
        $home_drawn = $home_standings['drawn'];
        $home_lost = $home_standings['lost'];
        $home_goals_for = $home_standings['goals_for'] + $home_score;
        $home_goals_against = $home_standings['goals_against'] + $away_score;

        $away_points = $away_standings['points'];
        $away_played = $away_standings['played'] + 1;
        $away_won = $away_standings['won'];
        $away_drawn = $away_standings['drawn'];
        $away_lost = $away_standings['lost'];
        $away_goals_for = $away_standings['goals_for'] + $away_score;
        $away_goals_against = $away_standings['goals_against'] + $home_score;

        if ($home_score > $away_score) {
            $home_points += 3;
            $home_won += 1;
            $away_lost += 1;
        } elseif ($home_score < $away_score) {
            $away_points += 3;
            $away_won += 1;
            $home_lost += 1;
        } else {
            $home_points += 1;
            $away_points += 1;
            $home_drawn += 1;
            $away_drawn += 1;
        }

        // Update home team standings
        $conn->query("UPDATE standings SET 
            points = $home_points, 
            played = $home_played, 
            won = $home_won, 
            drawn = $home_drawn, 
            lost = $home_lost, 
            goals_for = $home_goals_for, 
            goals_against = $home_goals_against 
            WHERE group_id = $group_id AND team_id = $home_team_id");

        // Update away team standings
        $conn->query("UPDATE standings SET 
            points = $away_points, 
            played = $away_played, 
            won = $away_won, 
            drawn = $away_drawn, 
            lost = $away_lost, 
            goals_for = $away_goals_for, 
            goals_against = $away_goals_against 
            WHERE group_id = $group_id AND team_id = $away_team_id");

        echo "<div class='alert alert-success'>Standings updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: Teams not assigned to any group.</div>";
    }
}
?>