body {
    background-color: #f8f9fa;
    font-family: "Inter", sans-serif;
    line-height: 1.5;
    color: var(--color-text-primary);
    background-color: var(--color-bg-secondary);
}

.navbar {
    margin-bottom: 20px;
}

.table {
    margin-top: 20px;
}

.alert {
    margin-top: 20px;
}

img {
    max-width: 100%;
    height: auto;
}

/* Custom Styles for Matches Page */
.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.card-title {
    font-size: 1.25rem;
    font-weight: bold;
}

.card-subtitle {
    font-size: 0.9rem;
    color: #666;
}

.card-text {
    font-size: 0.9rem;
    color: #333;
}

.nav-tabs .nav-link {
    font-weight: bold;
    color: #333;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

.text-danger {
    font-weight: bold;
}

.card-link {
    color: inherit;
    text-decoration: none;
    display: block;
    cursor: pointer;
    transition: all 0.3s ease;
}

.card-link:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    background-image: linear-gradient(to bottom right, #f0f0f0, #ffffff);
}

.card-body {
    padding: 20px;
}

.card-title {
    font-weight: 600;
}

.card-link:hover .card {
    border-bottom: 4px solid transparent;
    border-image: linear-gradient(to right, #007bff, #00c853);
    border-image-slice: 1;
}

/* Match Styles */
.match {
    background-color: var(--color-bg-primary);
    display: flex;
    flex-direction: column;
    min-width: 600px;
    border-radius: 10px;
    box-shadow: 0 0 2px 0 rgba(48, 48, 48, 0.1), 0 4px 4px 0 rgba(48, 48, 48, 0.1);
}

.match-header {
    display: flex;
    padding: 16px;
    border-bottom: 2px solid rgba(48, 48, 48, 0.1);
}

.match-status {
    background-color: var(--color-bg-alert);
    color: var(--color-text-alert);
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    line-height: 1;
    margin-right: auto;
}

.match-status:before {
    content: "";
    display: block;
    width: 6px;
    height: 6px;
    background-color: currentcolor;
    border-radius: 50%;
    margin-right: 8px;
}

.match-tournament {
    display: flex;
    align-items: center;
    font-weight: 600;
}

.match-tournament img {
    width: 20px;
    margin-right: 12px;
}

.match-actions {
    display: flex;
    margin-left: auto;
}

.btn-icon {
    border: none;
    background-color: transparent;
    color: var(--color-text-icon);
    display: flex;
    align-items: center;
    justify-content: center;
}

.match-content {
    display: flex;
    position: relative;
}

.column {
    padding: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(100% / 3);
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.team-logo {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--color-bg-primary);
    box-shadow: 0 4px 4px 0 rgba(48, 48, 48, 0.15), 0 0 0 15px var(--color-bg-secondary);
}

.team-logo img {
    width: 50px;
}

.team-name {
    text-align: center;
    margin-top: 24px;
    font-size: 20px;
    font-weight: 600;
}

.match-details {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.match-date,
.match-referee {
    font-size: 14px;
    color: var(--color-text-secondary);
}

.match-date strong,
.match-referee strong {
    color: var(--color-text-primary);
}

.match-score {
    margin-top: 12px;
    display: flex;
    align-items: center;
}

.match-score-number {
    font-size: 48px;
    font-weight: 600;
    line-height: 1;
}

.match-score-number--leading {
    color: var(--color-theme-primary);
}

.match-score-divider {
    font-size: 28px;
    font-weight: 700;
    line-height: 1;
    color: var(--color-text-icon);
    margin-left: 10px;
    margin-right: 10px;
}

.match-time-lapsed {
    color: #DF9443;
    font-size: 14px;
    font-weight: 600;
    margin-top: 8px;
}

.match-referee {
    margin-top: 12px;
}

.match-bet-options {
    display: flex;
    margin-top: 8px;
    padding-bottom: 12px;
}

.match-bet-option {
    margin-left: 4px;
    margin-right: 4px;
    border: 1px solid var(--color-text-icon);
    background-color: #F9F9F9;
    border-radius: 2px;
    color: var(--color-text-secondary);
    font-size: 14px;
    font-weight: 600;
    padding: 4px 8px;
}

.match-bet-place {
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    border: none;
    background-color: var(--color-theme-primary);
    border-radius: 6px;
    padding: 10px 48px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    box-shadow: 0 4px 8px 0 rgba(48, 48, 48, 0.25);
}

.container {
    padding-top: 20px; /* Add padding to avoid overlap with the navbar */
}

/* Importing Google font - Open Sans */
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap");
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Open Sans", sans-serif;
}
body {
    height: 100vh;
    width: 100%;
    background: url("images/hero-bg.jpg") center/cover no-repeat;
}
header {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 10;
    padding: 0 10px;
}
.navbar {
    display: flex;
    padding: 22px 0;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    justify-content: space-between;
}
.navbar .hamburger-btn {
    display: none;
    color: #fff;
    cursor: pointer;
    font-size: 1.5rem;
}
.navbar .logo {
    gap: 10px;
    display: flex;
    align-items: center;
    text-decoration: none;
}
.navbar .logo img {
    width: 40px;
    border-radius: 50%;
}
.navbar .logo h2 {
    color: #fff;
    font-weight: 600;
    font-size: 1.7rem;
}
.navbar .links {
    display: flex;
    gap: 35px;
    list-style: none;
    align-items: center;
}
.navbar .close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    display: none;
    color: #000;
    cursor: pointer;
}
.navbar .links a {
    color: #fff;
    font-size: 1.1rem;
    font-weight: 500;
    text-decoration: none;
    transition: 0.1s ease;
}
.navbar .links a:hover {
    color: #19e8ff;
}
.navbar .login-btn {
    border: none;
    outline: none;
    background: #fff;
    color: #275360;
    font-size: 1rem;
    font-weight: 600;
    padding: 10px 18px;
    border-radius: 3px;
    cursor: pointer;
    transition: 0.15s ease;
}
.navbar .login-btn:hover {
    background: #ddd;
}
.form-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 10;
    width: 100%;
    opacity: 0;
    pointer-events: none;
    max-width: 720px;
    background: #fff;
    border: 2px solid #fff;
    transform: translate(-50%, -70%);
}
.show-popup .form-popup {
    opacity: 1;
    pointer-events: auto;
    transform: translate(-50%, -50%);
    transition: transform 0.3s ease, opacity 0.1s;
}
.form-popup .close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    color: #878484;
    cursor: pointer;
}
.blur-bg-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    height: 100%;
    width: 100%;
    opacity: 0;
    pointer-events: none;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: 0.1s ease;
}
.show-popup .blur-bg-overlay {
    opacity: 1;
    pointer-events: auto;
}
.form-popup .form-box {
    display: flex;
}
.form-box .form-details {
    width: 100%;
    color: #fff;
    max-width: 330px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.login .form-details {
    padding: 0 40px;
    background: url("images/login-img.jpg");
    background-position: center;
    background-size: cover;
}
.signup .form-details {
    padding: 0 20px;
    background: url("images/signup-img.jpg");
    background-position: center;
    background-size: cover;
}
.form-box .form-content {
    width: 100%;
    padding: 35px;
}
.form-box h2 {
    text-align: center;
    margin-bottom: 29px;
}
form .input-field {
    position: relative;
    height: 50px;
    width: 100%;
    margin-top: 20px;
}
.input-field input {
    height: 100%;
    width: 100%;
    background: none;
    outline: none;
    font-size: 0.95rem;
    padding: 0 15px;
    border: 1px solid #717171;
    border-radius: 3px;
}
.input-field input:focus {
    border: 1px solid #00bcd4;
}
.input-field label {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    color: #4a4646;
    pointer-events: none;
    transition: 0.2s ease;
}
.input-field input:is(:focus, :valid) {
    padding: 16px 15px 0;
}
.input-field input:is(:focus, :valid)~label {
    transform: translateY(-120%);
    color: #00bcd4;
    font-size: 0.75rem;
}
.form-box a {
    color: #00bcd4;
    text-decoration: none;
}
.form-box a:hover {
    text-decoration: underline;
}
form :where(.forgot-pass-link, .policy-text) {
    display: inline-flex;
    margin-top: 13px;
    font-size: 0.95rem;
}
form button {
    width: 100%;
    color: #fff;
    border: none;
    outline: none;
    padding: 14px 0;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 3px;
    cursor: pointer;
    margin: 25px 0;
    background: #00bcd4;
    transition: 0.2s ease;
}
form button:hover {
    background: #0097a7;
}
.form-content .bottom-link {
    text-align: center;
}
.form-popup .signup,
.form-popup.show-signup .login {
    display: none;
}
.form-popup.show-signup .signup {
    display: flex;
}
.signup .policy-text {
    display: flex;
    margin-top: 14px;
    align-items: center;
}
.signup .policy-text input {
    width: 14px;
    height: 14px;
    margin-right: 7px;
}
@media (max-width: 950px) {
    .navbar :is(.hamburger-btn, .close-btn) {
        display: block;
    }
    .navbar {
        padding: 15px 0;
    }
    .navbar .logo img {
        display: none;
    }
    .navbar .logo h2 {
        font-size: 1.4rem;
    }
    .navbar .links {
        position: fixed;
        top: 0;
        z-index: 10;
        left: -100%;
        display: block;
        height: 100vh;
        width: 100%;
        padding-top: 60px;
        text-align: center;
        background: #fff;
        transition: 0.2s ease;
    }
    .navbar .links.show-menu {
        left: 0;
    }
    .navbar .links a {
        display: inline-flex;
        margin: 20px 0;
        font-size: 1.2rem;
        color: #000;
    }
    .navbar .links a:hover {
        color: #00BCD4;
    }
    .navbar .login-btn {
        font-size: 0.9rem;
        padding: 7px 10px;
    }
}
@media (max-width: 760px) {
    .form-popup {
        width: 95%;
    }
    .form-box .form-details {
        display: none;
    }
    .form-box .form-content {
        padding: 30px 20px;
    }
}

