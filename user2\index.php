<?php
include '../includes/db.php';

// Set page title for the header
$page_title = 'BRC Live - Barberian <PERSON> Cup Live Scores';

// Include the modern header
include '../includes/modern_header.php';

// Fetch Matches
$matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                         FROM matches 
                         JOIN leagues ON matches.league_id = leagues.id 
                         JOIN teams t1 ON matches.home_team_id = t1.id 
                         JOIN teams t2 ON matches.away_team_id = t2.id 
                         ORDER BY matches.date ASC");
?>

<!-- Custom Styles for Livescore -->
<style>
    /* Hero Section Styles */
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        padding: 4rem 0 6rem 0;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('images/bg_1.PNG') center/cover;
        opacity: 0.1;
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        color: var(--text-gray);
        margin-bottom: 2.5rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-cta {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Match Filter Tabs */
    .match-filters {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 0.5rem;
        margin-bottom: 2rem;
        display: flex;
        gap: 0.5rem;
        overflow-x: auto;
    }

    .filter-tab {
        background: transparent;
        border: none;
        color: var(--text-gray);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        position: relative;
    }

    .filter-tab.active {
        background: var(--gradient-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .filter-tab:hover:not(.active) {
        background: rgba(233, 69, 96, 0.1);
        color: var(--highlight-color);
    }

    /* Match Cards */
    .match-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-decoration: none;
        color: inherit;
        display: block;
    }

    .match-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-hover);
        border-color: var(--highlight-color);
        text-decoration: none;
        color: inherit;
    }

    .match-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--gradient-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .match-card:hover::before {
        opacity: 1;
    }

    .match-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .league-name {
        background: var(--gradient-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .match-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .match-status.live {
        background: var(--highlight-color);
        color: white;
        animation: pulse 2s infinite;
    }

    .match-status.upcoming {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        border: 1px solid #ffc107;
    }

    .match-status.finished {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 1px solid #28a745;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .teams-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .team {
        text-align: center;
        flex: 1;
    }

    .team-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-light);
        margin-bottom: 0.5rem;
    }

    .team-logo {
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.5rem auto;
        font-size: 1.5rem;
        color: white;
    }

    .vs-divider {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 1rem;
    }

    .vs-text {
        color: var(--text-gray);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .score-display {
        background: var(--primary-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-light);
        min-width: 80px;
        text-align: center;
    }

    .match-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid var(--border-color);
        font-size: 0.9rem;
        color: var(--text-gray);
    }

    .match-date {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .live-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--highlight-color);
        font-weight: 600;
    }

    .live-dot {
        width: 8px;
        height: 8px;
        background: var(--highlight-color);
        border-radius: 50%;
        animation: pulse 1.5s infinite;
    }

    .matches-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .hero-cta {
            flex-direction: column;
            align-items: center;
        }

        .match-filters {
            padding: 0.25rem;
        }

        .filter-tab {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .teams-container {
            flex-direction: column;
            gap: 1rem;
        }

        .vs-divider {
            order: 2;
            margin: 0;
        }

        .team {
            order: 1;
        }

        .team:last-child {
            order: 3;
        }

        .score-display {
            font-size: 1.2rem;
            padding: 0.5rem 0.75rem;
        }

        .match-info {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }

        .matches-grid {
            grid-template-columns: 1fr;
        }
    }

    /* About Section Styles */
    .about-section {
        padding: 5rem 0;
        background: var(--bg-light);
    }

    .about-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .about-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-hover);
        border-color: var(--highlight-color);
    }

    .about-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .about-card:hover .about-image {
        transform: scale(1.05);
    }

    .about-content {
        padding: 1.5rem;
    }

    .about-title {
        color: var(--text-light);
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .about-title i {
        color: var(--highlight-color);
        font-size: 1.1rem;
    }

    .about-text {
        color: var(--text-gray);
        line-height: 1.6;
        margin: 0;
    }

    /* Video Section Styles */
    .video-section {
        padding: 5rem 0;
        background: var(--primary-color);
    }

    .video-container {
        position: relative;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: var(--shadow-hover);
    }

    .video-wrapper {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
    }

    .video-wrapper iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
    }

    /* Additional responsive styles */
    @media (max-width: 768px) {
        .about-section {
            padding: 3rem 0;
        }

        .video-section {
            padding: 3rem 0;
        }

        .about-image {
            height: 150px;
        }

        .about-content {
            padding: 1rem;
        }

        .about-title {
            font-size: 1.1rem;
        }
    }
</style>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Barberian Ramadhan Cup</h1>
            <p class="hero-subtitle">
                Experience the thrill of live football with real-time scores, match updates, and comprehensive statistics from Tanzania's premier tournament.
            </p>
            <div class="hero-cta">
                <a href="standings.php" class="btn-modern">
                    <i class="fas fa-trophy"></i> View Standings
                </a>
                <a href="matches.php" class="btn-secondary">
                    <i class="fas fa-calendar-alt"></i> All Matches
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Matches Section -->
<section class="matches-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title">Live Matches & Results</h2>
                <p class="section-subtitle">Stay updated with real-time scores and match information</p>
            </div>
        </div>

        <!-- Match Filter Tabs -->
        <div class="match-filters">
            <button class="filter-tab active" data-filter="all">
                <i class="fas fa-list"></i> All Matches
            </button>
            <button class="filter-tab" data-filter="live">
                <i class="fas fa-circle live-dot"></i> Live
            </button>
            <button class="filter-tab" data-filter="upcoming">
                <i class="fas fa-clock"></i> Upcoming
            </button>
            <button class="filter-tab" data-filter="finished">
                <i class="fas fa-check-circle"></i> Finished
            </button>
        </div>

        <!-- Matches Grid -->
        <div class="matches-grid">
            <?php
            if ($matches->num_rows > 0) {
                while ($row = $matches->fetch_assoc()) {
                    $status_class = strtolower($row['status']);
                    $status_icon = '';
                    $status_text = ucfirst($row['status']);

                    switch($row['status']) {
                        case 'live':
                            $status_icon = '<i class="fas fa-circle live-dot"></i>';
                            break;
                        case 'upcoming':
                            $status_icon = '<i class="fas fa-clock"></i>';
                            break;
                        case 'finished':
                            $status_icon = '<i class="fas fa-check-circle"></i>';
                            break;
                    }

                    $match_date = date('M d, Y', strtotime($row['date']));
                    $match_time = date('H:i', strtotime($row['date']));
            ?>
                <div class="match-card" data-status="<?php echo $status_class; ?>">
                    <div class="match-header">
                        <span class="league-name"><?php echo htmlspecialchars($row['league_name']); ?></span>
                        <span class="match-status <?php echo $status_class; ?>">
                            <?php echo $status_icon . ' ' . $status_text; ?>
                        </span>
                    </div>

                    <div class="teams-container">
                        <div class="team">
                            <div class="team-logo">
                                <?php echo strtoupper(substr($row['home_team'], 0, 2)); ?>
                            </div>
                            <div class="team-name"><?php echo htmlspecialchars($row['home_team']); ?></div>
                        </div>

                        <div class="vs-divider">
                            <span class="vs-text">VS</span>
                            <div class="score-display">
                                <?php
                                if ($row['status'] == 'upcoming') {
                                    echo '-:-';
                                } else {
                                    echo $row['home_team_score'] . ':' . $row['away_team_score'];
                                }
                                ?>
                            </div>
                        </div>

                        <div class="team">
                            <div class="team-logo">
                                <?php echo strtoupper(substr($row['away_team'], 0, 2)); ?>
                            </div>
                            <div class="team-name"><?php echo htmlspecialchars($row['away_team']); ?></div>
                        </div>
                    </div>

                    <div class="match-info">
                        <div class="match-date">
                            <i class="fas fa-calendar-alt"></i>
                            <?php echo $match_date . ' at ' . $match_time; ?>
                        </div>
                        <?php if ($row['status'] == 'live'): ?>
                            <div class="live-indicator">
                                <span class="live-dot"></span>
                                <span id="elapsed-time-<?php echo $row['id']; ?>">Live</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php
                }
            } else {
                echo '<div class="col-12"><div class="alert alert-info">No matches available at the moment.</div></div>';
            }
            ?>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title">About Barberian Park</h2>
                <p class="section-subtitle">Discover the premier entertainment destination in Dar es Salaam</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="about-card">
                    <img src="images/b1.jpg" alt="Barberian Park" class="about-image">
                    <div class="about-content">
                        <h3 class="about-title">
                            <i class="fas fa-map-marker-alt"></i>
                            Prime Location
                        </h3>
                        <p class="about-text">
                            Located in the prestigious Oyster Bay area of Dar es Salaam, Tanzania, near the beautiful Coco Beach. Our strategic location makes us easily accessible for all football enthusiasts.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="about-card">
                    <img src="images/b2.jpg" alt="Tournament Spirit" class="about-image">
                    <div class="about-content">
                        <h3 class="about-title">
                            <i class="fas fa-users"></i>
                            Community Unity
                        </h3>
                        <p class="about-text">
                            The Barberian Ramadhan Cup offers a unique platform for teams to compete, celebrate the spirit of unity, and foster strong community connections through the beautiful game of football.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="about-card">
                    <img src="images/b3.jpg" alt="Entertainment Hub" class="about-image">
                    <div class="about-content">
                        <h3 class="about-title">
                            <i class="fas fa-star"></i>
                            Entertainment Hub
                        </h3>
                        <p class="about-text">
                            Beyond football, Barberian Park offers go-kart racing, dining, and beverages in a lively atmosphere suitable for all ages. Open daily for endless fun and memorable experiences.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Video Section -->
<section class="video-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title">Experience the Action</h2>
                <p class="section-subtitle">Watch highlights and feel the excitement of Barberian Ramadhan Cup</p>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="video-container">
                    <div class="video-wrapper">
                        <iframe
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                            title="Barberian Ramadhan Cup Highlights"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Match filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterTabs = document.querySelectorAll('.filter-tab');
    const matchCards = document.querySelectorAll('.match-card');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Update active tab
            filterTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Filter matches
            matchCards.forEach(card => {
                const status = card.getAttribute('data-status');

                if (filter === 'all' || status === filter) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });

    // Live timer functionality
    function updateLiveTimers() {
        const liveMatches = document.querySelectorAll('[data-status="live"]');

        liveMatches.forEach(match => {
            const timerId = match.querySelector('[id^="elapsed-time-"]');
            if (timerId) {
                // This would typically connect to a real-time data source
                // For demo purposes, we'll just show "Live"
                timerId.textContent = 'Live';
            }
        });
    }

    // Update timers every second for live matches
    setInterval(updateLiveTimers, 1000);

    // Initial call
    updateLiveTimers();
});

// Add fade-in animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);
</script>

<?php include '../includes/modern_footer.php'; ?>
