<?php
include '../includes/db.php';
include '../includes/header.php';
include 'check_login.php';
if (!isset($_GET['id'])) {
    header("Location: teams.php");
    exit();
}

$team_id = $_GET['id'];

// Fetch Team Details
$team = $conn->query("SELECT teams.*, leagues.name as league_name FROM teams 
                      JOIN leagues ON teams.league_id = leagues.id 
                      WHERE teams.id = $team_id")->fetch_assoc();

// Fetch Squad (Players)
$squad = $conn->query("SELECT * FROM players WHERE team_id = $team_id");

// Fetch Team News (Injuries, Next Match)
$news = $conn->query("SELECT * FROM team_news WHERE team_id = $team_id");

// Fetch Sponsors
$sponsors = $conn->query("SELECT * FROM sponsors WHERE team_id = $team_id");

// Fetch Next Match
$next_match = $conn->query("SELECT * FROM matches WHERE (home_team_id = $team_id OR away_team_id = $team_id) 
                            AND date > NOW() ORDER BY date ASC LIMIT 1")->fetch_assoc();
?>

<h1><?php echo $team['name']; ?></h1>
<img src="<?php echo $team['logo']; ?>" alt="Team Logo" width="100">
<p><strong>League:</strong> <?php echo $team['league_name']; ?></p>
<p><strong>Description:</strong> <?php echo $team['description'] ?? "No description available"; ?></p>

<h2>Squad</h2>
<table class="table">
    <thead>
        <tr>
            <th>Name</th>
            <th>Position</th>
            <th>Age</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($player = $squad->fetch_assoc()) { ?>
        <tr>
            <td><?php echo $player['name']; ?></td>
            <td><?php echo $player['position']; ?></td>
            <td><?php echo $player['age']; ?></td>
        </tr>
        <?php } ?>
    </tbody>
</table>







<a href="teams.php" class="btn btn-secondary">Back to Teams</a>

<?php include '../includes/footer.php'; ?>
