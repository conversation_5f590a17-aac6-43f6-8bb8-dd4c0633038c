<?php
include '../includes/db.php';

$data = json_decode(file_get_contents('php://input'), true);
$matchId = $data['match_id'];

if (!is_numeric($matchId)) {
    echo json_encode(['success' => false, 'message' => 'Invalid match ID.']);
    exit;
}

// Use a prepared statement here too, for consistency and security.
$stmt = $conn->prepare("SELECT me.minute, me.event_type, t.name as team_name, p.name as player_name
                        FROM match_events me
                        LEFT JOIN teams t ON me.team_id = t.id
                        LEFT JOIN players p ON me.player_id = p.id
                        WHERE me.match_id = ?
                        ORDER BY me.minute");
$stmt->bind_param("i", $matchId);
$stmt->execute();
$result = $stmt->get_result();

$events = [];
while ($row = $result->fetch_assoc()) {
    $events[] = $row;
}

echo json_encode($events);

$stmt->close();
$conn->close();
?>