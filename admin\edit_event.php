<?php
// events.php

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection and authentication checks
include '../includes/db.php';
include 'check_login.php';
date_default_timezone_set('Africa/Dar_es_Salaam');

// ---------- Endpoint Processing (AJAX Requests) ----------
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    header('Content-Type: application/json');

    // ADD EVENT
    if ($action == 'add') {
        $data = json_decode(file_get_contents("php://input"), true);
        // Require match_id, minute, event_type, team_id
        if (!isset($data['match_id']) || !isset($data['minute']) || !isset($data['event_type']) || !isset($data['team_id'])) {
            echo json_encode(['success' => false, 'message' => 'Missing required fields.']);
            exit;
        }
        $match_id   = intval($data['match_id']);
        $minute     = intval($data['minute']);
        $event_type = $data['event_type'];
        $team_id    = intval($data['team_id']);
        $player_id  = (isset($data['player_id']) && $data['player_id'] !== "") ? intval($data['player_id']) : null;
        $created_at = date('Y-m-d H:i:s');

        $stmt = $conn->prepare("INSERT INTO match_events (match_id, event_type, team_id, player_id, minute, created_at) VALUES (?, ?, ?, ?, ?, ?)");
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => $conn->error]);
            exit;
        }
        $stmt->bind_param("isiiis", $match_id, $event_type, $team_id, $player_id, $minute, $created_at);
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'id' => $stmt->insert_id]);
        } else {
            echo json_encode(['success' => false, 'message' => $conn->error]);
        }
        $stmt->close();
        $conn->close();
        exit;
    }

    // UPDATE EVENT
    if ($action == 'update') {
        if (!isset($_GET['id'])) {
            echo json_encode(['success' => false, 'message' => 'No event ID provided.']);
            exit;
        }
        $id = intval($_GET['id']);
        $data = json_decode(file_get_contents("php://input"), true);
        if (!isset($data['match_id']) || !isset($data['minute']) || !isset($data['event_type']) || !isset($data['team_id'])) {
            echo json_encode(['success' => false, 'message' => 'Missing required fields.']);
            exit;
        }
        $match_id   = intval($data['match_id']);
        $minute     = intval($data['minute']);
        $event_type = $data['event_type'];
        $team_id    = intval($data['team_id']);
        $player_id  = (isset($data['player_id']) && $data['player_id'] !== "") ? intval($data['player_id']) : null;

        $stmt = $conn->prepare("UPDATE match_events SET match_id = ?, event_type = ?, team_id = ?, player_id = ?, minute = ? WHERE id = ?");
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => $conn->error]);
            exit;
        }
        $stmt->bind_param("isiiii", $match_id, $event_type, $team_id, $player_id, $minute, $id);
        if ($stmt->execute()) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => $conn->error]);
        }
        $stmt->close();
        $conn->close();
        exit;
    }

    // DELETE EVENT
    if ($action == 'delete') {
        if (!isset($_GET['id'])) {
            echo json_encode(['success' => false, 'message' => 'No event ID provided.']);
            exit;
        }
        $id = intval($_GET['id']);
        $stmt = $conn->prepare("DELETE FROM match_events WHERE id = ?");
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => $conn->error]);
            exit;
        }
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => $conn->error]);
        }
        $stmt->close();
        $conn->close();
        exit;
    }
}
// ---------- End of Endpoint Processing ----------

// ---------- Pagination Setup ----------
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
if ($page < 1) $page = 1;
$limit = 10;
$offset = ($page - 1) * $limit;

$countQuery = "SELECT COUNT(*) as total FROM match_events";
$countResult = $conn->query($countQuery);
$total = 0;
if ($countResult) {
    $row = $countResult->fetch_assoc();
    $total = $row['total'];
}
$totalPages = ceil($total / $limit);
// ---------- End Pagination Setup ----------

// Fetch events with pagination and joined match, team, and player names.
$query = "SELECT me.id, me.match_id, CONCAT(ht.name, ' vs ', at.name) AS match_name, me.event_type, me.team_id, me.player_id, me.minute, me.created_at,
                 t.name AS team_name, p.name AS player_name
          FROM match_events me
          LEFT JOIN matches m ON me.match_id = m.id
          LEFT JOIN teams ht ON m.home_team_id = ht.id
          LEFT JOIN teams at ON m.away_team_id = at.id
          LEFT JOIN teams t ON me.team_id = t.id
          LEFT JOIN players p ON me.player_id = p.id
          ORDER BY me.created_at DESC
          LIMIT $limit OFFSET $offset";
$eventsResult = $conn->query($query);
if (!$eventsResult) {
    die("Error fetching events: " . $conn->error);
}

// Fetch teams for dropdown (full list)
$teamsResult = $conn->query("SELECT id, name FROM teams ORDER BY name ASC");
if (!$teamsResult) {
    die("Error fetching teams: " . $conn->error);
}
$teams = [];
while ($row = $teamsResult->fetch_assoc()) {
    $teams[] = $row;
}

// Fetch players for dropdown (including team_id for filtering)
$playersResult = $conn->query("SELECT id, name, team_id FROM players ORDER BY name ASC");
if (!$playersResult) {
    die("Error fetching players: " . $conn->error);
}
$players = [];
while ($row = $playersResult->fetch_assoc()) {
    $players[] = $row;
}

// Fetch matches for dropdown with additional team IDs
$matchesResult = $conn->query("SELECT m.id, m.home_team_id, m.away_team_id, CONCAT(ht.name, ' vs ', at.name) AS match_name 
                               FROM matches m 
                               LEFT JOIN teams ht ON m.home_team_id = ht.id 
                               LEFT JOIN teams at ON m.away_team_id = at.id 
                               ORDER BY m.date ASC");
if (!$matchesResult) {
    die("Error fetching matches: " . $conn->error);
}
$matches = [];
while ($row = $matchesResult->fetch_assoc()) {
    $matches[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Admin - Manage Match Events</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- SweetAlert2 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <!-- Animate.css -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
  <style>
    body {
      background: #fff;
      font-family: 'Inter', sans-serif;
    }
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    .btn-custom {
      background-color: #623ce6;
      color: #fff;
      transition: background-color 0.3s;
    }
    .btn-custom:hover {
      background-color: #4a2fbf;
    }
    .table thead {
      background-color: #623ce6;
      color: #fff;
    }
    .table-striped tbody tr:nth-of-type(odd) {
      background-color: rgba(98, 60, 230, 0.1);
    }
    .swal2-popup {
      font-size: 1.2rem;
      border-radius: 15px;
    }
  </style>
</head>
<body>
  <!-- Header with Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="#">Admin Panel</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
        <div class="navbar-nav ms-auto">
          <a class="nav-link" href="index.php">Dashboard</a>
          <a class="nav-link active" href="#">Events</a>
          <a class="nav-link" href="matches.php">Matches</a>
          <a class="nav-link" href="teams.php">Teams</a>
          <a class="nav-link" href="logout.php">Logout</a>
        </div>
      </div>
    </div>
  </nav>
  
  <!-- Main Container -->
  <div class="container my-5">
    <div class="card p-4">
      <h2 class="text-center mb-4">Manage Match Events</h2>
      <div class="mb-3 text-end">
        <button class="btn btn-custom" id="addEventBtn">Add New Event</button>
      </div>
      <div class="table-responsive">
        <table class="table table-striped align-middle">
          <thead>
            <tr>
              <!-- Serial number column -->
              <th>#</th>
              <th>Match</th>
              <th>Minute</th>
              <th>Event</th>
              <th>Team</th>
              <th>Player</th>
              <th>Created At</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="eventsTableBody">
            <?php 
              // Serial number based on current page offset
              $serial = $offset + 1;
              while ($event = $eventsResult->fetch_assoc()):
            ?>
            <tr data-id="<?php echo $event['id']; ?>" data-matchid="<?php echo $event['match_id']; ?>">
              <td><?php echo $serial++; ?></td>
              <td><?php echo $event['match_name']; ?></td>
              <td><?php echo $event['minute']; ?>'</td>
              <td><?php echo ucfirst(str_replace('_', ' ', $event['event_type'])); ?></td>
              <td><?php echo $event['team_name']; ?></td>
              <td><?php echo $event['player_name']; ?></td>
              <td><?php echo date('d M Y H:i', strtotime($event['created_at'])); ?></td>
              <td>
                <button class="btn btn-sm btn-warning editEventBtn">Edit</button>
                <button class="btn btn-sm btn-danger deleteEventBtn">Delete</button>
              </td>
            </tr>
            <?php endwhile; ?>
          </tbody>
        </table>
      </div>
      <!-- Pagination Controls -->
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          <?php if ($page > 1): ?>
            <li class="page-item"><a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a></li>
          <?php else: ?>
            <li class="page-item disabled"><span class="page-link">Previous</span></li>
          <?php endif; ?>

          <?php for ($i = 1; $i <= $totalPages; $i++): ?>
            <li class="page-item <?php if ($i == $page) echo 'active'; ?>">
              <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
            </li>
          <?php endfor; ?>

          <?php if ($page < $totalPages): ?>
            <li class="page-item"><a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a></li>
          <?php else: ?>
            <li class="page-item disabled"><span class="page-link">Next</span></li>
          <?php endif; ?>
        </ul>
      </nav>
    </div>
  </div>

  <!-- Modal for Add/Edit Event -->
  <div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content animate__animated animate__fadeInDown">
        <div class="modal-header">
          <h5 class="modal-title" id="eventModalLabel">Add/Edit Event</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="eventForm">
            <input type="hidden" id="eventId" name="id" value="">
            <!-- Match Dropdown -->
            <div class="mb-3">
              <label for="matchId" class="form-label">Match</label>
              <select class="form-select" id="matchId" name="match_id" required>
                <option value="">Select Match</option>
                <?php foreach ($matches as $match): ?>
                  <option value="<?php echo $match['id']; ?>" data-home="<?php echo $match['home_team_id']; ?>" data-away="<?php echo $match['away_team_id']; ?>">
                    <?php echo $match['match_name']; ?>
                  </option>
                <?php endforeach; ?>
              </select>
            </div>
            <!-- Team Dropdown (filtered by selected match) -->
            <div class="mb-3">
              <label for="teamId" class="form-label">Team</label>
              <select class="form-select" id="teamId" name="team_id" required>
                <option value="">Select Team</option>
                <?php foreach ($teams as $team): ?>
                  <option value="<?php echo $team['id']; ?>"><?php echo $team['name']; ?></option>
                <?php endforeach; ?>
              </select>
            </div>
            <div class="mb-3">
              <label for="eventMinute" class="form-label">Minute</label>
              <input type="number" class="form-control" id="eventMinute" name="minute" required>
            </div>
            <div class="mb-3">
              <label for="eventType" class="form-label">Event Type</label>
              <select class="form-select" id="eventType" name="event_type" required>
                <option value="">Select Event Type</option>
                <option value="goal">Goal</option>
                <option value="assist">Assist</option>
                <option value="yellow_card">Yellow Card</option>
                <option value="red_card">Red Card</option>
                <option value="penalty">Penalty</option>
                <option value="clean_sheet">Clean Sheet</option>
                <option value="first_half">First Half</option>
                <option value="half_time">Half Time</option>
                <option value="second_half">Second Half</option>
              </select>
            </div>
            <!-- Players Dropdown (filtered by selected team) -->
            <div class="mb-3">
              <label for="playerId" class="form-label">Player</label>
              <select class="form-select" id="playerId" name="player_id">
                <option value="">Select Player (Optional)</option>
                <?php foreach ($players as $player): ?>
                  <option value="<?php echo $player['id']; ?>" data-team="<?php echo $player['team_id']; ?>"><?php echo $player['name']; ?></option>
                <?php endforeach; ?>
              </select>
            </div>
            <button type="submit" class="btn btn-custom w-100">Save Event</button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
      const eventForm = document.getElementById('eventForm');
      let editingEventId = null;

      // Store original player options for filtering
      const allPlayerOptions = Array.from(document.querySelectorAll('#playerId option[data-team]'));

      // When team dropdown changes, filter players by team
      document.getElementById('teamId').addEventListener('change', function() {
        const selectedTeamId = this.value;
        const playerDropdown = document.getElementById('playerId');
        playerDropdown.innerHTML = '<option value="">Select Player (Optional)</option>';
        allPlayerOptions.forEach(option => {
          if (option.getAttribute('data-team') === selectedTeamId) {
            playerDropdown.appendChild(option);
          }
        });
      });

      // Build a dictionary mapping team id to team name from team dropdown options
      const teamDropdown = document.getElementById('teamId');
      const allTeamOptions = Array.from(teamDropdown.options);
      const teamMapping = {};
      allTeamOptions.forEach(option => {
        if (option.value !== "") {
          teamMapping[option.value] = option.textContent;
        }
      });

      // When match dropdown changes, update team dropdown to show only the two teams for the selected match
      document.getElementById('matchId').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const homeTeam = selectedOption.getAttribute('data-home');
        const awayTeam = selectedOption.getAttribute('data-away');
        if (!this.value) {
          // Revert to full team list if no match is selected
          teamDropdown.innerHTML = "";
          const defaultOpt = document.createElement('option');
          defaultOpt.value = "";
          defaultOpt.textContent = "Select Team";
          teamDropdown.appendChild(defaultOpt);
          allTeamOptions.forEach(option => {
            if(option.value !== "") {
              teamDropdown.appendChild(option.cloneNode(true));
            }
          });
        } else {
          // Build options only for the two teams
          teamDropdown.innerHTML = "";
          const defaultOpt = document.createElement('option');
          defaultOpt.value = "";
          defaultOpt.textContent = "Select Team";
          teamDropdown.appendChild(defaultOpt);
          if (homeTeam) {
            const opt = document.createElement('option');
            opt.value = homeTeam;
            opt.textContent = teamMapping[homeTeam] || ("Team " + homeTeam);
            teamDropdown.appendChild(opt);
          }
          if (awayTeam) {
            const opt = document.createElement('option');
            opt.value = awayTeam;
            opt.textContent = teamMapping[awayTeam] || ("Team " + awayTeam);
            teamDropdown.appendChild(opt);
          }
        }
        teamDropdown.dispatchEvent(new Event('change'));
      });

      // Open modal for Add Event
      document.getElementById('addEventBtn').addEventListener('click', function() {
        editingEventId = null;
        eventForm.reset();
        document.getElementById('eventModalLabel').textContent = 'Add New Event';
        eventModal.show();
      });

      // Delegate Edit and Delete actions on table rows
      document.getElementById('eventsTableBody').addEventListener('click', function(e) {
        const row = e.target.closest('tr');
        const eventId = row.getAttribute('data-id');
        if (e.target.classList.contains('editEventBtn')) {
          editingEventId = eventId;
          document.getElementById('eventId').value = eventId;
          document.getElementById('matchId').value = row.getAttribute('data-matchid');
          document.getElementById('matchId').dispatchEvent(new Event('change'));
          document.getElementById('eventMinute').value = row.children[2].textContent.replace("'", "");
          let evtType = row.children[3].textContent.trim().toLowerCase().replace(/ /g, '_');
          document.getElementById('eventType').value = evtType;
          document.querySelectorAll('#teamId option').forEach(option => {
            if (option.textContent.trim() === row.children[4].textContent.trim()) {
              option.selected = true;
            }
          });
          document.getElementById('teamId').dispatchEvent(new Event('change'));
          document.querySelectorAll('#playerId option').forEach(option => {
            if (option.textContent.trim() === row.children[5].textContent.trim()) {
              option.selected = true;
            }
          });
          document.getElementById('eventModalLabel').textContent = 'Edit Event';
          eventModal.show();
        }
        if (e.target.classList.contains('deleteEventBtn')) {
          Swal.fire({
            title: 'Are you sure?',
            text: "This event will be permanently deleted.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            customClass: {
              popup: 'animate__animated animate__fadeInDown'
            }
          }).then((result) => {
            if (result.isConfirmed) {
              fetch(`?action=delete&id=${eventId}`, {
                method: 'POST'
              })
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  row.remove();
                  Swal.fire({
                    title: 'Deleted!',
                    text: 'Event deleted successfully.',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false,
                    customClass: { popup: 'animate__animated animate__zoomIn' }
                  });
                }
              });
            }
          });
        }
      });

      // Handle form submission for Add/Edit event
      eventForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(eventForm);
        const eventData = {};
        formData.forEach((value, key) => eventData[key] = value);

        const url = editingEventId ? `?action=update&id=${editingEventId}` : '?action=add';
        const method = editingEventId ? 'PUT' : 'POST';

        fetch(url, {
          method: method,
          body: JSON.stringify(eventData),
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            if (editingEventId) {
              const row = document.querySelector(`tr[data-id="${editingEventId}"]`);
              row.setAttribute('data-matchid', eventData.match_id);
              const matchText = document.querySelector(`#matchId option[value="${eventData.match_id}"]`).textContent;
              row.children[1].textContent = matchText;
              row.children[2].textContent = eventData.minute + "'";
              row.children[3].textContent = eventData.event_type.charAt(0).toUpperCase() + eventData.event_type.slice(1);
              row.children[4].textContent = document.querySelector(`#teamId option[value="${eventData.team_id}"]`).textContent;
              row.children[5].textContent = eventData.player_id ? document.querySelector(`#playerId option[value="${eventData.player_id}"]`).textContent : '';
            } else {
              const newId = data.id;
              const tbody = document.getElementById('eventsTableBody');
              const newRow = document.createElement('tr');
              newRow.setAttribute('data-id', newId);
              newRow.setAttribute('data-matchid', eventData.match_id);
              const matchText = document.querySelector(`#matchId option[value="${eventData.match_id}"]`).textContent;
              const teamText = document.querySelector(`#teamId option[value="${eventData.team_id}"]`).textContent;
              const playerText = eventData.player_id ? document.querySelector(`#playerId option[value="${eventData.player_id}"]`).textContent : '';
              newRow.innerHTML = 
                `<td>${newId}</td>
                <td>${matchText}</td>
                <td>${eventData.minute}'</td>
                <td>${eventData.event_type.charAt(0).toUpperCase() + eventData.event_type.slice(1)}</td>
                <td>${teamText}</td>
                <td>${playerText}</td>
                <td>${new Date().toLocaleString()}</td>
                <td>
                  <button class="btn btn-sm btn-warning editEventBtn">Edit</button>
                  <button class="btn btn-sm btn-danger deleteEventBtn">Delete</button>
                </td>`;
              tbody.prepend(newRow);
            }
            Swal.fire({
              title: editingEventId ? 'Updated!' : 'Added!',
              text: editingEventId ? 'Event updated successfully.' : 'Event added successfully.',
              icon: 'success',
              timer: 1500,
              showConfirmButton: false,
              customClass: { popup: 'animate__animated animate__zoomIn' }
            });
            eventModal.hide();
          }
        });
      });
    });
  </script>
</body>
</html>
<?php 
$conn->close();
include '../includes/footer.php'; 
?>
