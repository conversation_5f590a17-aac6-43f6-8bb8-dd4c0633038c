<?php
// ../includes/get_players.php
include 'db.php'; // Include your database connection

$data = json_decode(file_get_contents('php://input'), true);

if (isset($data['team_id'])) {
    $teamId = $data['team_id'];
    $matchId = $data['match_id']; // Get the match ID

    $sql = "SELECT p.id, p.name FROM players p
            JOIN team_players tp ON p.id = tp.player_id
            WHERE tp.team_id = ?
            AND tp.match_id = ?"; // Include the match ID.
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $teamId, $matchId); // Bind both parameters
    $stmt->execute();
    $result = $stmt->get_result();

    $players = [];
    while ($row = $result->fetch_assoc()) {
        $players[] = $row;
    }

    echo json_encode($players);
} else {
    echo json_encode([]); // Return an empty array if team_id is not set
}

$conn->close();

?>