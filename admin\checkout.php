<?php
include '../includes/db.php';
session_start();

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Sample product data (replace with your actual product/cart logic)
$products = [
    [
        'id' => 2,
        'name' => 'Premium Product',
        'price' => 4000,
        'quantity' => 2
    ]
];

// Calculate total amount
$totalAmount = array_reduce($products, function($carry, $item) {
    return $carry + ($item['price'] * $item['quantity']);
}, 0);

// Generate transaction reference
$txRef = generateTransactionRef();

// Store transaction reference in session for verification
$_SESSION['current_transaction'] = $txRef;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout | Your Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .checkout-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .payment-methods img {
            height: 30px;
            margin-right: 10px;
        }
        .btn-pay {
            background: linear-gradient(135deg, #3a7bd5, #00d2ff);
            border: none;
            padding: 12px;
            font-weight: bold;
            letter-spacing: 1px;
        }
        .product-item {
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="checkout-container">
            <h2 class="text-center mb-4">Complete Your Purchase</h2>
            
            <div class="order-summary mb-4">
                <h5><i class="fas fa-shopping-cart"></i> Order Summary</h5>
                <?php foreach($products as $product): ?>
                    <div class="product-item">
                        <div class="d-flex justify-content-between">
                            <span><?php echo htmlspecialchars($product['name']); ?></span>
                            <span>₦<?php echo number_format($product['price'], 2); ?></span>
                        </div>
                        <div class="text-muted">Qty: <?php echo $product['quantity']; ?></div>
                    </div>
                <?php endforeach; ?>
                
                <div class="d-flex justify-content-between mt-3 fw-bold">
                    <span>Total:</span>
                    <span>₦<?php echo number_format($totalAmount, 2); ?></span>
                </div>
            </div>
            
            <form id="paymentForm">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="tx_ref" value="<?php echo $txRef; ?>">
                <input type="hidden" name="amount" value="<?php echo $totalAmount; ?>">
                
                <div class="mb-3">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                </div>
                
                <div class="mb-3">
                    <label for="phone" class="form-label">Phone Number</label>
                    <input type="tel" class="form-control" id="phone" name="phone" required>
                </div>
                
                <div class="payment-methods mb-4">
                    <h5><i class="fas fa-credit-card"></i> Payment Method</h5>
                    <div class="d-flex flex-wrap">
                        <img src="https://flutterwave.com/images/visa.svg" alt="Visa">
                        <img src="https://flutterwave.com/images/mastercard.svg" alt="Mastercard">
                        <img src="https://flutterwave.com/images/verve.svg" alt="Verve">
                        <img src="https://flutterwave.com/images/ussd.svg" alt="USSD">
                    </div>
                </div>
                
                <button type="button" onclick="makePayment()" class="btn btn-primary btn-pay w-100">
                    <i class="fas fa-lock"></i> Pay ₦<?php echo number_format($totalAmount, 2); ?>
                </button>
            </form>
        </div>
    </div>

    <script src="https://checkout.flutterwave.com/v3.js"></script>
    <script>
        function makePayment() {
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const amount = <?php echo $totalAmount; ?>;
            const tx_ref = "<?php echo $txRef; ?>";
            
            if (!email || !phone) {
                alert('Please fill in all required fields');
                return;
            }

            FlutterwaveCheckout({
                public_key: "<?php echo FLW_PUBLIC_KEY; ?>",
                tx_ref: tx_ref,
                amount: amount,
                currency: "NGN",
                country: "NG",
                payment_options: "card,ussd,banktransfer",
                customer: {
                    email: email,
                    phone_number: phone,
                    name: "Customer Name" // You can collect this from a form field
                },
                callback: function(response) {
                    // Verify the payment
                    verifyPayment(response.transaction_id);
                },
                onclose: function() {
                    // Handle when modal is closed
                    console.log('Payment modal closed');
                },
                customizations: {
                    title: "Your Store Name",
                    description: "Payment for items in cart",
                    logo: "<?php echo BASE_URL; ?>/logo.png"
                }
            });
        }
        
        function verifyPayment(transaction_id) {
            fetch('verify_payment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    transaction_id: transaction_id,
                    tx_ref: "<?php echo $txRef; ?>",
                    csrf_token: "<?php echo $_SESSION['csrf_token']; ?>"
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.href = 'payment_success.php?reference=' + data.tx_ref;
                } else {
                    window.location.href = 'payment_failed.php?error=' + encodeURIComponent(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.location.href = 'payment_failed.php?error=Verification failed';
            });
        }
    </script>
</body>
</html>