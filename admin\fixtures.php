<?php
include '../includes/db.php';
// include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';

// Update Fixture (handle edit submission)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_fixture'])) {
    $fixture_id = $_POST['fixture_id'];
    $league_id = $_POST['league_id'];
    $match_id = $_POST['match_id'];
    $round = $_POST['round'];

    $sql = "UPDATE fixtures SET league_id = '$league_id', match_id = '$match_id', round = '$round' WHERE id = '$fixture_id'";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Fixture updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Create Fixture
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_fixture'])) {
    $league_id = $_POST['league_id'];
    $match_id = $_POST['match_id'];
    $round = $_POST['round'];

    // Check if a fixture already exists for this match
    $checkQuery = "SELECT id FROM fixtures WHERE match_id = '$match_id'";
    $checkResult = $conn->query($checkQuery);
    if ($checkResult->num_rows > 0) {
        echo "<div class='alert alert-danger'>Fixture for this match already added, please select another match.</div>";
    } else {
        $sql = "INSERT INTO fixtures (league_id, match_id, round) VALUES ('$league_id', '$match_id', '$round')";
        if ($conn->query($sql)) {
            echo "<div class='alert alert-success'>Fixture created successfully!</div>";
        } else {
            echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
        }
    }
}

// Fetch Fixtures sorted by newest match date first
$fixtures = $conn->query("SELECT fixtures.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team, matches.date 
                          FROM fixtures 
                          JOIN leagues ON fixtures.league_id = leagues.id 
                          JOIN matches ON fixtures.match_id = matches.id 
                          JOIN teams t1 ON matches.home_team_id = t1.id 
                          JOIN teams t2 ON matches.away_team_id = t2.id 
                          ORDER BY matches.date DESC");

// Fetch leagues for dropdowns
$leagues = $conn->query("SELECT * FROM leagues");

// Fetch matches sorted by newest date first for dropdowns
$matches = $conn->query("SELECT matches.*, t1.name as home_team, t2.name as away_team 
                         FROM matches 
                         JOIN teams t1 ON matches.home_team_id = t1.id 
                         JOIN teams t2 ON matches.away_team_id = t2.id 
                         ORDER BY matches.date DESC");
?>

<!DOCTYPE html>
<html>
<head>
    <title>Manage Fixtures</title>
    <style>
        /* Minimalist Figma-inspired styling */
        body {
            font-family: 'Helvetica Neue', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 2rem auto;
            background-color: #fff;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }
        form .mb-3 {
            margin-bottom: 1rem;
        }
        select, input[type="text"], input[type="number"], button {
            width: 100%;
            padding: 0.75rem;
            font-size: 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            cursor: pointer;
            background-color: #333;
            color: #fff;
            border: none;
            margin-top: 0.5rem;
        }
        button:hover {
            background-color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 2rem;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 0.75rem;
            text-align: left;
        }
        .btn {
            padding: 0.5rem 1rem;
            text-decoration: none;
            display: inline-block;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-warning {
            background-color: #f0ad4e;
            color: #fff;
        }
        .btn-danger {
            background-color: #d9534f;
            color: #fff;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .alert-danger {
            background-color: #f2dede;
            color: #a94442;
        }
        /* Modal Styles */
        .modal {
            display: none; 
            position: fixed; 
            z-index: 1000; 
            left: 0;
            top: 0;
            width: 100%; 
            height: 100%; 
            overflow: auto; 
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 2rem;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 4px;
            position: relative;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            right: 1rem;
            top: 1rem;
            cursor: pointer;
        }
        .close:hover,
        .close:focus {
            color: black;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Manage Fixtures</h1>

    <!-- Create Fixture Form -->
    <form method="POST" class="mb-4">
        <div class="mb-3">
            <select name="league_id" required>
                <option value="">Select League</option>
                <?php while ($row = $leagues->fetch_assoc()) { ?>
                <option value="<?php echo $row['id']; ?>"><?php echo $row['name']; ?></option>
                <?php } ?>
            </select>
        </div>
        <div class="mb-3">
            <select name="match_id" required>
                <option value="">Select Match</option>
                <?php while ($row = $matches->fetch_assoc()) { ?>
                <option value="<?php echo $row['id']; ?>">
                    <?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?> (<?php echo $row['date']; ?>)
                </option>
                <?php } ?>
            </select>
        </div>
        <div class="mb-3">
            <select name="round" required>
                <option value="">Select Round</option>
                <option value="Group Stage">Group Stage</option>
                <option value="Quarter Final">Quarter Final</option>
                <option value="Semi Final">Semi Final</option>
                <option value="Final">Final</option>
            </select>
        </div>
        <button type="submit" name="create_fixture">Create Fixture</button>
    </form>

    <!-- Display Fixtures -->
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>League</th>
                <th>Match</th>
                <th>Round</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            // Loop through fixtures; embed necessary data attributes for editing.
            while ($row = $fixtures->fetch_assoc()) { ?>
            <tr>
                <td><?php echo $row['id']; ?></td>
                <td><?php echo $row['league_name']; ?></td>
                <td><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?> (<?php echo $row['date']; ?>)</td>
                <td><?php echo $row['round']; ?></td>
                <td>
                    <a href="#" class="btn btn-warning edit-btn"
                       data-fixture_id="<?php echo $row['id']; ?>"
                       data-league_id="<?php echo $row['league_id']; ?>"
                       data-match_id="<?php echo $row['match_id']; ?>"
                       data-round="<?php echo $row['round']; ?>">Edit</a>
                    <a href="delete_fixture.php?id=<?php echo $row['id']; ?>" class="btn btn-danger" onclick="return confirm('Are you sure?')">Delete</a>
                </td>
            </tr>
            <?php } ?>
        </tbody>
    </table>
</div>

<!-- Edit Fixture Modal -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Edit Fixture</h2>
        <form id="editFixtureForm" method="POST">
            <input type="hidden" name="fixture_id" id="edit_fixture_id">
            <div class="mb-3">
                <select name="league_id" id="edit_league_id" required>
                    <option value="">Select League</option>
                    <?php 
                    // Reset pointer for leagues dropdown
                    $leagues->data_seek(0);
                    while ($row = $leagues->fetch_assoc()) { ?>
                    <option value="<?php echo $row['id']; ?>"><?php echo $row['name']; ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="mb-3">
                <select name="match_id" id="edit_match_id" required>
                    <option value="">Select Match</option>
                    <?php 
                    // Reset pointer for matches dropdown
                    $matches->data_seek(0);
                    while ($row = $matches->fetch_assoc()) { ?>
                    <option value="<?php echo $row['id']; ?>">
                        <?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?> (<?php echo $row['date']; ?>)
                    </option>
                    <?php } ?>
                </select>
            </div>
            <div class="mb-3">
                <select name="round" id="edit_round" required>
                    <option value="">Select Round</option>
                    <option value="Group Stage">Group Stage</option>
                    <option value="Quarter Final">Quarter Final</option>
                    <option value="Semi Final">Semi Final</option>
                    <option value="Final">Final</option>
                </select>
            </div>
            <button type="submit" name="update_fixture">Save Changes</button>
        </form>
    </div>
</div>

<script>
    // Modal functionality
    var modal = document.getElementById("editModal");
    var span = document.getElementsByClassName("close")[0];

    // Close modal when clicking on the "X"
    span.onclick = function() {
        modal.style.display = "none";
    }

    // Close modal when clicking outside of modal content
    window.onclick = function(event) {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    }

    // Attach click event listeners to all edit buttons
    var editButtons = document.getElementsByClassName("edit-btn");
    for (var i = 0; i < editButtons.length; i++) {
        editButtons[i].addEventListener("click", function(event) {
            event.preventDefault();
            var fixtureId = this.getAttribute("data-fixture_id");
            var leagueId = this.getAttribute("data-league_id");
            var matchId = this.getAttribute("data-match_id");
            var round = this.getAttribute("data-round");

            // Populate the modal form fields with current fixture data
            document.getElementById("edit_fixture_id").value = fixtureId;
            document.getElementById("edit_league_id").value = leagueId;
            document.getElementById("edit_match_id").value = matchId;
            document.getElementById("edit_round").value = round;

            // Display the modal
            modal.style.display = "block";
        });
    }
</script>
<?php include '../includes/footer.php'; ?>
</body>
</html>
