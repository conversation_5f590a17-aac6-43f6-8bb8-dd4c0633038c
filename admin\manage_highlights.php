<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Handle form submission for adding/editing highlights
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $match_id = $_POST['match_id'];
    $video_title = $_POST['video_title'];
    $video_description = $_POST['video_description'];
    $video_link = $_POST['video_link'];
    $thumbnail = $_FILES['thumbnail']['name'];

    // Validate inputs
    if (empty($match_id)) {
        echo "<div class='alert alert-danger'>Please select a match.</div>";
    } else {
        // Upload thumbnail
        $thumbnail_path = "../assets/uploads/thumbnails/" . basename($thumbnail);
        move_uploaded_file($_FILES['thumbnail']['tmp_name'], $thumbnail_path);

        // Insert into database
        $sql = "INSERT INTO match_highlights (match_id, video_title, video_description, video_link, thumbnail) 
                VALUES ('$match_id', '$video_title', '$video_description', '$video_link', '$thumbnail_path')";
        if ($conn->query($sql)) {
            echo "<div class='alert alert-success'>Highlight added successfully!</div>";
        } else {
            echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
        }
    }
}

// Handle delete request
if (isset($_GET['delete'])) { // Check if 'delete' key exists in $_GET
    $id = $_GET['delete'];
    $sql = "DELETE FROM match_highlights WHERE id = $id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Highlight deleted successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Fetch all finished matches with team names and date
$matches = $conn->query("SELECT matches.*, t1.name as home_team, t2.name as away_team 
                         FROM matches 
                         JOIN teams t1 ON matches.home_team_id = t1.id 
                         JOIN teams t2 ON matches.away_team_id = t2.id 
                         WHERE matches.status = 'finished'");

// Fetch all highlights with team names and date
$highlights_query = "SELECT match_highlights.*, t1.name as home_team, t2.name as away_team, matches.date 
                     FROM match_highlights 
                     JOIN matches ON match_highlights.match_id = matches.id 
                     JOIN teams t1 ON matches.home_team_id = t1.id 
                     JOIN teams t2 ON matches.away_team_id = t2.id";
$highlights = $conn->query($highlights_query);

// Check if the query was successful
if (!$highlights) {
    die("Database query failed: " . $conn->error); // Display error and stop execution
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Match Highlights</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-container {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .thumbnail-preview {
            max-width: 100px;
            max-height: 100px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Manage Match Highlights</h1>

        <!-- Add Highlight Form -->
        <div class="form-container mb-4">
            <h3>Add Highlight</h3>
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label class="form-label">Select Match</label>
                    <select name="match_id" class="form-control" required>
                        <option value="">Select a match</option>
                        <?php while ($match = $matches->fetch_assoc()) { ?>
                            <option value="<?php echo $match['id']; ?>">
                                <?php echo $match['home_team'] . " vs " . $match['away_team'] . " (" . date('d M Y H:i', strtotime($match['date'])) . ")"; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Video Title</label>
                    <input type="text" name="video_title" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Video Description</label>
                    <textarea name="video_description" class="form-control" rows="3" required></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Video Link (e.g., YouTube)</label>
                    <input type="url" name="video_link" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Thumbnail</label>
                    <input type="file" name="thumbnail" class="form-control" required>
                    <small class="text-muted">Upload a thumbnail image for the video.</small>
                </div>
                <button type="submit" class="btn btn-primary">Add Highlight</button>
            </form>
        </div>

        <!-- Display Highlights -->
        <h2>Highlights List</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Match</th>
                    <th>Video Title</th>
                    <th>Thumbnail</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($highlights->num_rows > 0) { ?>
                    <?php while ($row = $highlights->fetch_assoc()) { ?>
                        <tr>
                            <td><?php echo $row['id']; ?></td>
                            <td>
                                <?php echo $row['home_team'] . " vs " . $row['away_team'] . " (" . date('d M Y H:i', strtotime($row['date'])) . ")"; ?>
                            </td>
                            <td><?php echo $row['video_title']; ?></td>
                            <td>
                                <img src="<?php echo $row['thumbnail']; ?>" alt="Thumbnail" class="thumbnail-preview">
                            </td>
                            <td>
                                <a href="edit_highlight.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
                                <a href="manage_highlights.php?delete=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</a>
                            </td>
                        </tr>
                    <?php } ?>
                <?php } else { ?>
                    <tr>
                        <td colspan="5" class="text-center">No highlights found.</td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</body>
</html>

<?php include '../includes/footer.php'; ?>