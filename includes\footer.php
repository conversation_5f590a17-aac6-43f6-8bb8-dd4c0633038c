<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />

    <style>
    /* Importing Google font - Open Sans */
    @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Open Sans', sans-serif;
    }

    body {
        min-height: 100vh; /* Ensure the body takes at least the full viewport height */
        display: flex;        /* Use flexbox for layout */
        flex-direction: column; /* Stack children vertically */
    }
    /* Add a wrapper for your main content */
   .content-wrapper {
       flex: 1; /* Grow to fill available space */
       /* Other styles for your content area */
       padding: 20px; /* Example padding */
   }

    .footer {
        width: 100%; /* Full width */
        background: #10182F;
        /* Removed absolute positioning, top, left, transform */
    }

    .footer .footer-row {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 3.5rem;
        padding: 60px;
    }

    .footer-row .footer-col h4 {
        color: #fff;
        font-size: 1.2rem;
        font-weight: 400;
    }

    .footer-col .links {
        margin-top: 20px;
    }

    .footer-col .links li {
        list-style: none;
        margin-bottom: 10px;
    }

    .footer-col .links li a {
        text-decoration: none;
        color: #bfbfbf;
    }

    .footer-col .links li a:hover {
        color: #fff;
    }

    .footer-col p {
        margin: 20px 0;
        color: #bfbfbf;
        max-width: 300px;
    }

    .footer-col form {
        display: flex;
        gap: 5px;
    }

    .footer-col input {
        height: 40px;
        border-radius: 6px;
        background: none;
        width: 100%;
        outline: none;
        border: 1px solid #7489C6;
        caret-color: #fff;
        color: #fff;
        padding-left: 10px;
    }

    .footer-col input::placeholder {
        color: #ccc;
    }

    .footer-col form button {
        background: #fff;
        outline: none;
        border: none;
        padding: 10px 15px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        transition: 0.2s ease;
    }

    .footer-col form button:hover {
        background: #cecccc;
    }

    .footer-col .icons {
        display: flex;
        margin-top: 30px;
        gap: 30px;
        cursor: pointer;
    }

    .footer-col .icons i {
        color: #afb6c7;
    }

    .footer-col .icons i:hover {
        color: #fff;
    }

    @media (max-width: 768px) {
        .footer {
            /* No need for position: relative; here */
            width: 100%;
            border-radius: 0;
        }

        .footer .footer-row {
            padding: 20px;
            gap: 1rem;
        }

        .footer-col form {
            display: block;
        }

        .footer-col form :where(input, button) {
            width: 100%;
        }

        .footer-col form button {
            margin: 10px 0 0 0;
        }
    }
    .footer-col, .footer-col * { /* Apply to footer-col and all its children */
  color: white !important;
}
    </style>
</head>
<body>
    

    <footer class="footer">
        <div class="footer-row">
            
            <div class="footer-col">
                <h4>Modules</h4>
                <ul class="links">
                    <li><a href="teams.php">All Teams</a></li>
                    <li><a href="matches.php">Manage Matches</a></li>
                    <li><a href="#">Fixture</a></li>
                    <li><a href="standings.php">Standing</a></li>
                    <li><a href="#">Updates</a></li>
                    <li><a href="#">Player Stats</a></li>
                </ul>
            </div>
            
            <div class="footer-col">
            Digitize Creates Copyright &copy; 
                <script>
                  document.write(new Date().getFullYear());
                </script>
                <div class="icons">
 
  <a href="https://wa.me/255783533622" target="_blank">
    <i class="fa-brands fa-whatsapp"></i>
  </a>
  <a href="https://www.instagram.com/digitizecreates?utm_source=ig_web_button_share_sheet&igsh=ZDNlZDc0MzIxNw==" target="_blank">
    <i class="fa-brands fa-instagram"></i>
  </a>
</div>
            </div>
        </div>
    </footer>
</body>
</html>