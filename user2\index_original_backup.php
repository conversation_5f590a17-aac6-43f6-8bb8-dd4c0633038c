<?php
include '../includes/db.php';

// Set page title for the header
$page_title = 'BRC Live - Barberian <PERSON> Cup Live Scores';

// Include the modern header
include '../includes/modern_header.php';

// Fetch Matches
$matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team
                         FROM matches
                         JOIN leagues ON matches.league_id = leagues.id
                         JOIN teams t1 ON matches.home_team_id = t1.id
                         JOIN teams t2 ON matches.away_team_id = t2.id
                         ORDER BY matches.date ASC");
?>

<!-- Custom Styles for Livescore -->
<style>
    /* Hero Section Styles */
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        padding: 4rem 0 6rem 0;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('images/bg_1.PNG') center/cover;
        opacity: 0.1;
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        color: var(--text-gray);
        margin-bottom: 2.5rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-cta {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Match Filter Tabs */
    .match-filters {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 0.5rem;
        margin-bottom: 2rem;
        display: flex;
        gap: 0.5rem;
        overflow-x: auto;
    }

    .filter-tab {
        background: transparent;
        border: none;
        color: var(--text-gray);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        position: relative;
    }

    .filter-tab.active {
        background: var(--gradient-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .filter-tab:hover:not(.active) {
        background: rgba(233, 69, 96, 0.1);
        color: var(--highlight-color);
    }

    /* Match Cards */
    .match-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-decoration: none;
        color: inherit;
    }

    .match-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-hover);
        border-color: var(--highlight-color);
        text-decoration: none;
        color: inherit;
    }

    .match-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--gradient-primary);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .match-card:hover::before {
        opacity: 1;
    }

    .match-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .league-name {
        background: var(--gradient-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .match-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .match-status.live {
        background: var(--highlight-color);
        color: white;
        animation: pulse 2s infinite;
    }

    .match-status.upcoming {
        background: rgba(255, 193, 7, 0.2);
        color: #ffc107;
        border: 1px solid #ffc107;
    }

    .match-status.finished {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 1px solid #28a745;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .teams-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .team {
        text-align: center;
        flex: 1;
    }

    .team-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-light);
        margin-bottom: 0.5rem;
    }

    .team-logo {
        width: 50px;
        height: 50px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.5rem auto;
        font-size: 1.5rem;
        color: white;
    }

    .vs-divider {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 1rem;
    }

    .vs-text {
        color: var(--text-gray);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .score-display {
        background: var(--primary-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-light);
        min-width: 80px;
        text-align: center;
    }

    .match-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid var(--border-color);
        font-size: 0.9rem;
        color: var(--text-gray);
    }

    .match-date {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .live-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--highlight-color);
        font-weight: 600;
    }

    .live-dot {
        width: 8px;
        height: 8px;
        background: var(--highlight-color);
        border-radius: 50%;
        animation: pulse 1.5s infinite;
    }

    /* About Section */
    .about-section {
        padding: 4rem 0;
        background: var(--secondary-color);
    }

    .about-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .about-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-hover);
    }

    .about-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .about-content {
        padding: 1.5rem;
    }

    .about-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-light);
        margin-bottom: 1rem;
    }

    .about-text {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Video Section */
    .video-section {
        padding: 4rem 0;
        background: var(--primary-color);
    }

    .video-container {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: var(--shadow);
    }

    .video-container video {
        width: 100%;
        height: auto;
        max-height: 500px;
        object-fit: cover;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .hero-cta {
            flex-direction: column;
            align-items: center;
        }

        .match-filters {
            padding: 0.25rem;
        }

        .filter-tab {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .teams-container {
            flex-direction: column;
            gap: 1rem;
        }

        .vs-divider {
            order: 2;
            margin: 0;
        }

        .team {
            order: 1;
        }

        .team:last-child {
            order: 3;
        }

        .score-display {
            font-size: 1.2rem;
            padding: 0.5rem 0.75rem;
        }

        .match-info {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }
    }

    /* Loading Animation */
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-color);
        border-radius: 50%;
        border-top-color: var(--highlight-color);
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--text-gray);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--border-color);
    }

    .empty-state h3 {
        color: var(--text-light);
        margin-bottom: 0.5rem;
    }
</style>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Barberian Ramadhan Cup</h1>
            <p class="hero-subtitle">
                Experience the thrill of live football with real-time scores, match updates, and comprehensive statistics from Tanzania's premier tournament.
            </p>
            <div class="hero-cta">
                <a href="standings.php" class="btn-modern">
                    <i class="fas fa-trophy"></i> View Standings
                </a>
                <a href="matches.php" class="btn-secondary">
                    <i class="fas fa-calendar-alt"></i> All Matches
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Live Scores Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title">Live Scores & Matches</h2>
                <p class="section-subtitle">Stay updated with real-time match results and upcoming fixtures</p>

                <!-- Match Filter Tabs -->
                <div class="match-filters">
                    <button class="filter-tab active" data-filter="all">
                        <i class="fas fa-list"></i> All Matches
                    </button>
                    <button class="filter-tab" data-filter="live">
                        <i class="fas fa-circle live-dot"></i> Live
                    </button>
                    <button class="filter-tab" data-filter="upcoming">
                        <i class="fas fa-clock"></i> Upcoming
                    </button>
                    <button class="filter-tab" data-filter="finished">
                        <i class="fas fa-check-circle"></i> Finished
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Matches Container -->
<div class="container">
    <div class="matches-container" id="matchesContainer">
        <!-- All Matches -->
        <div class="matches-grid" data-filter="all">
            <?php
            // Reset pointer for reuse
            $matches->data_seek(0);
            while ($row = $matches->fetch_assoc()) {
                $status_class = $row['status'];
                $status_icon = '';
                $status_text = ucfirst($row['status']);

                switch($row['status']) {
                    case 'live':
                        $status_icon = '<i class="fas fa-circle live-dot"></i>';
                        break;
                    case 'upcoming':
                        $status_icon = '<i class="fas fa-clock"></i>';
                        break;
                    case 'finished':
                        $status_icon = '<i class="fas fa-check-circle"></i>';
                        break;
                }
            ?>
                <a href="match_details.php?match_id=<?php echo $row['id']; ?>" class="match-card" data-status="<?php echo $row['status']; ?>">
                    <div class="match-header">
                        <span class="league-name"><?php echo $row['league_name']; ?></span>
                        <span class="match-status <?php echo $status_class; ?>">
                            <?php echo $status_icon . ' ' . $status_text; ?>
                        </span>
                    </div>

                    <div class="teams-container">
                        <div class="team">
                            <div class="team-logo">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="team-name"><?php echo $row['home_team']; ?></div>
                        </div>

                        <div class="vs-divider">
                            <span class="vs-text">VS</span>
                            <div class="score-display">
                                <?php echo $row['home_team_score']; ?> - <?php echo $row['away_team_score']; ?>
                            </div>
                        </div>

                        <div class="team">
                            <div class="team-logo">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="team-name"><?php echo $row['away_team']; ?></div>
                        </div>
                    </div>

                    <div class="match-info">
                        <div class="match-date">
                            <i class="fas fa-calendar-alt"></i>
                            <span><?php echo date('d M Y, H:i', strtotime($row['date'])); ?></span>
                        </div>

                        <?php if ($row['status'] == 'live') { ?>
                            <div class="live-indicator">
                                <div class="live-dot"></div>
                                <span id="elapsed-time-<?php echo $row['id']; ?>">Live</span>
                            </div>
                            <script>
                                // JavaScript to update elapsed time for live matches
                                (function() {
                                    function updateElapsedTime() {
                                        const startTime = <?php echo strtotime($row['date']); ?>;
                                        const currentTime = Math.floor(Date.now() / 1000);
                                        let elapsedTime = currentTime - startTime;
                                        let minutes = Math.floor(elapsedTime / 60);

                                        const element = document.getElementById('elapsed-time-<?php echo $row['id']; ?>');
                                        if (element && minutes >= 0) {
                                            element.textContent = `${minutes}'`;
                                        }
                                    }

                                    updateElapsedTime();
                                    setInterval(updateElapsedTime, 60000); // Update every minute
                                })();
                            </script>
                        <?php } ?>
                    </div>
                </a>
            <?php } ?>
        </div>
    </div>
</div>


    <div class="latest-news">
      <div class="container">
        <div class="row">
          <div class="col-12 title-section">
            <h2 class="heading">ABOUT BARBERIAN PARK</h2>
          </div>
        </div>
        <div class="row no-gutters">
    <div class="col-md-4">
        <div class="post-entry">
            <a href="#">
                <img src="images/b1.jpg" alt="Image" class="img-fluid"> 
            </a>
            <div class="caption">
                <div class="caption-inner">
                    <h3 class="mb-3">Barberian Park</h3>
                    <div class="author d-flex align-items-center">
                        <div class="text">
                            <p>is an entertainment venue located in the Oyster Bay area of Dar es Salaam, Tanzania, near Coco Beach.</p>
                        </div>
                    </div>
                </div>
            </div> 
        </div>
    </div>
    <div class="col-md-4">
        <div class="post-entry">
           
                <img src="images/b2.jpg" alt="Image" class="img-fluid"> <div class="caption">
                <div class="caption-inner">
                    <h2 class="mb-3">Barberian Park</h2>
                    <div class="author d-flex align-items-center">
                        
                        <div class="text">
                            <p>BRC offers a unique platform for teams to compete, celebrate the spirit of unity, and foster community connections.</p>
                            
                        </div>
                    </div>
                </div>
            </div> 
           
        </div>
    </div>
    <div class="col-md-4">
        <div class="post-entry">
            <a href="#">
                <img src="images/b3.jpg" alt="Image" class="img-fluid"> 
            </a>
            <div class="caption">
                <div class="caption-inner">
                    <h3 class="mb-3">Barberian Park Oyster Bay</h3>
                    <div class="author d-flex align-items-center">
                        
                        <div class="text">
                            <p>The park offers a variety of activities, including go-kart racing, making it a popular destination for thrill-seekers. Visitors can also enjoy a selection of food and beverages, contributing to a lively atmosphere suitable for all ages. The park is open daily, providing endless fun and memorable experiences.</p>
                            
                        </div>
                    </div>
                </div>
            </div> 
        </div>
    </div>
</div>

      </div>
    </div>
    

    

   

            </div>

            <!-- Video Section -->
<div class="video-section" style="padding: 50px 0;">
  <div class="container">
    <div class="row">
      <div class="col-12 title-section">
        <h2 class="heading">BARBERIAN PARK</h2>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <video width="100%" controls autoplay muted playsinline style="max-height: 500px; object-fit: cover;">
          <source src="images/video.mp4" type="video/mp4">
          Your browser does not support the video tag.
        </video>
      </div>
    </div>
  </div>
</div>



   



  </div>
  <!-- .site-wrap -->

  <script src="js/jquery-3.3.1.min.js"></script>
  <script src="js/jquery-migrate-3.0.1.min.js"></script>
  <script src="js/jquery-ui.js"></script>
  <script src="js/popper.min.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/owl.carousel.min.js"></script>
  <script src="js/jquery.stellar.min.js"></script>
  <script src="js/jquery.countdown.min.js"></script>
  <script src="js/bootstrap-datepicker.min.js"></script>
  <script src="js/jquery.easing.1.3.js"></script>
  <script src="js/aos.js"></script>
  <script src="js/jquery.fancybox.min.js"></script>
  <script src="js/jquery.sticky.js"></script>
  <script src="js/jquery.mb.YTPlayer.min.js"></script>
<!-- jQuery (if not already loaded) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Slick Slider JS -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
<script>
  $(document).ready(function(){
    $('.slider').slick({
      autoplay: true,
      autoplaySpeed: 4000,
      fade: true,
      arrows: false,
      dots: true
    });
  });
</script>
  <script src="js/main.js"></script>

</body>

</html>
<?php include '../includes/footer.php'; ?>