<?php
abstract class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    // Generic method to find a record by ID
    public function find($id) {
        $query = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        try {
            $stmt = $this->db->prepare($query);
            $stmt->execute(['id' => $id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->logError("Error finding record: " . $e->getMessage());
            return false;
        }
    }
    
    // Generic method to get all records
    public function all($conditions = [], $orderBy = null, $limit = null) {
        $query = "SELECT * FROM {$this->table}";
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', array_map(
                fn($key) => "$key = :$key",
                array_keys($conditions)
            ));
        }
        
        if ($orderBy) {
            $query .= " ORDER BY $orderBy";
        }
        
        if ($limit) {
            $query .= " LIMIT $limit";
        }
        
        try {
            $stmt = $this->db->prepare($query);
            $stmt->execute($conditions);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->logError("Error fetching records: " . $e->getMessage());
            return false;
        }
    }
    
    // Generic create method
    public function create($data) {
        $fields = implode(', ', array_keys($data));
        $values = ':' . implode(', :', array_keys($data));
        
        $query = "INSERT INTO {$this->table} ($fields) VALUES ($values)";
        
        try {
            $stmt = $this->db->prepare($query);
            $stmt->execute($data);
            return $this->db->lastInsertId();
        } catch (PDOException $e) {
            $this->logError("Error creating record: " . $e->getMessage());
            return false;
        }
    }
    
    // Generic update method
    public function update($id, $data) {
        $fields = implode('=?, ', array_keys($data)) . '=?';
        $query = "UPDATE {$this->table} SET $fields WHERE {$this->primaryKey} = ?";
        
        try {
            $stmt = $this->db->prepare($query);
            $values = array_values($data);
            $values[] = $id;
            return $stmt->execute($values);
        } catch (PDOException $e) {
            $this->logError("Error updating record: " . $e->getMessage());
            return false;
        }
    }
    
    // Generic delete method
    public function delete($id) {
        $query = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        
        try {
            $stmt = $this->db->prepare($query);
            return $stmt->execute(['id' => $id]);
        } catch (PDOException $e) {
            $this->logError("Error deleting record: " . $e->getMessage());
            return false;
        }
    }
    
    protected function logError($message) {
        error_log(date('[Y-m-d H:i:s] ') . $message . PHP_EOL, 3, ERROR_LOG);
    }
}
