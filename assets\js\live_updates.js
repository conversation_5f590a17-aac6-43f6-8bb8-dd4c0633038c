function fetchLiveMatchData(matchId) {
    setInterval(() => {
        fetch(`get_live_match_data.php?match_id=${matchId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('minute').innerText = data.minute;
                document.getElementById('home_score').innerText = data.home_score;
                document.getElementById('away_score').innerText = data.away_score;
            });
    }, 5000); // Fetch every 5 seconds
}