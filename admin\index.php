<?php
include '../includes/db.php';

session_start();
// Add authentication check here, redirect to login if not logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php"); // Replace 'login.php' with your login page
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>

    <style>
        /* =========== Google Fonts ============ */
        @import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap");

        /* =============== Globals ============== */
        * {
            font-family: "Ubuntu", sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --blue: #2a2185;
            --white: #fff;
            --gray: #f5f5f5;
            --black1: #222;
            --black2: #999;
        }

        body {
            min-height: 100vh;
            overflow-x: hidden; /* Prevent horizontal scrollbars */
            background-color: var(--gray); /* Add a background */
        }

        .container {
            position: relative;
            width: 100%;
        }

        /* =============== Navigation ================ */
        .navigation {
            position: fixed;
            width: 300px;
            height: 100%;
            background: var(--blue);
            border-left: 10px solid var(--blue);
            transition: 0.5s;
            overflow-y: auto; /* Enable vertical scrolling for sidebar */
            overflow-x: hidden; /* Hide horizontal scrollbar in sidebar */
        }
        .navigation.active {
            width: 80px;
        }

        .navigation ul {
            position: absolute;  /* Changed to absolute */
            top: 0;
            left: 0;
            width: 100%;
            padding-left: 0; /* Remove default padding */
        }

        .navigation ul li {
            position: relative;
            width: 100%;
            list-style: none;
            border-top-left-radius: 30px;
            border-bottom-left-radius: 30px;
        }

        .navigation ul li:hover,
        .navigation ul li.hovered {
            background-color: var(--white);
        }

        .navigation ul li:nth-child(1) {
            margin-bottom: 40px;
            pointer-events: none;
        }

        .navigation ul li a {
            position: relative;
            display: block; /* Changed to block */
            width: 100%;  /* Take up full width */
            display: flex;
            text-decoration: none;
            color: var(--white);
        }
        .navigation ul li:hover a,
        .navigation ul li.hovered a {
            color: var(--blue);
        }

        .navigation ul li a .icon {
            position: relative;
            display: flex; /* Use flexbox for vertical centering */
            align-items: center; /* Center vertically */
            justify-content: center; /* Center horizontally */
            min-width: 60px;
            height: 60px;
            /* line-height: 75px;  Removed - flexbox handles centering */
            text-align: center;
        }
        .navigation ul li a .icon ion-icon {
            font-size: 1.75rem;
        }

        .navigation ul li a .title {
            position: relative;
            display: block;
            padding: 0 10px;
            height: 60px;
            line-height: 60px;
            text-align: start;
            white-space: nowrap;
        }

        /* --------- curve outside ---------- */
        .navigation ul li:hover a::before,
        .navigation ul li.hovered a::before {
            content: "";
            position: absolute;
            right: 0;
            top: -50px;
            width: 50px;
            height: 50px;
            background-color: transparent;
            border-radius: 50%;
            box-shadow: 35px 35px 0 10px var(--white);
            pointer-events: none;
        }
        .navigation ul li:hover a::after,
        .navigation ul li.hovered a::after {
            content: "";
            position: absolute;
            right: 0;
            bottom: -50px;
            width: 50px;
            height: 50px;
            background-color: transparent;
            border-radius: 50%;
            box-shadow: 35px -35px 0 10px var(--white);
            pointer-events: none;
        }

        /* ===================== Main ===================== */
        .main {
            position: absolute;
            width: calc(100% - 300px);
            left: 300px;
            min-height: 100vh;
            background: var(--white);
            transition: 0.5s;
            padding: 20px; /* Add padding to the main content area */
        }
        .main.active {
            width: calc(100% - 80px);
            left: 80px;
        }

        .topbar {
            width: 100%;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            background-color: var(--white); /* Add background */
        }

        .toggle {
            position: relative;
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.5rem;
            cursor: pointer;
        }

        .search {
            position: relative;
            width: 400px;
            margin: 0 10px;
        }

        .search label {
            position: relative;
            width: 100%;
        }

        .search label input {
            width: 100%;
            height: 40px;
            border-radius: 40px;
            padding: 5px 20px;
            padding-left: 35px;
            font-size: 18px;
            outline: none;
            border: 1px solid var(--black2);
        }

        .search label ion-icon {
            position: absolute;
            top: 0;
            left: 10px;
            font-size: 1.2rem;
        }

        .user {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
        }

        .user img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover; /* Ensure the image covers the circle */
        }

        /* ======================= Cards ====================== */
        .cardBox {
            position: relative;
            width: 100%;
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 30px;
        }

        .cardBox .card {
            position: relative;
            background: var(--white);
            padding: 30px;
            border-radius: 20px;
            display: flex;
            justify-content: space-between;
            cursor: pointer;
            box-shadow: 0 7px 25px rgba(0, 0, 0, 0.08);
        }

        .cardBox .card .numbers {
            position: relative;
            font-weight: 500;
            font-size: 2.5rem;
            color: var(--blue);
        }

        .cardBox .card .cardName {
            color: var(--black2);
            font-size: 1.1rem;
            margin-top: 5px;
        }

        .cardBox .card .iconBx {
            font-size: 3.5rem;
            color: var(--black2);
        }

        .cardBox .card:hover {
            background: var(--blue);
        }
        .cardBox .card:hover .numbers,
        .cardBox .card:hover .cardName,
        .cardBox .card:hover .iconBx {
            color: var(--white);
        }

        /* ================== Order Details List ============== */
        .details {
            position: relative;
            width: 100%;
            padding: 20px;
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-gap: 30px;
            /* margin-top: 10px; */
        }

        .details .recentOrders {
            position: relative;
            display: grid;
            min-height: 500px;
            background: var(--white);
            padding: 20px;
            box-shadow: 0 7px 25px rgba(0, 0, 0, 0.08);
            border-radius: 20px;
        }

        .details .cardHeader {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .cardHeader h2 {
            font-weight: 600;
            color: var(--blue);
        }
        .cardHeader .btn {
            position: relative;
            padding: 5px 10px;
            background: var(--blue);
            text-decoration: none;
            color: var(--white);
            border-radius: 6px;
        }

        .details table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .details table thead td {
            font-weight: 600;
        }
        .details .recentOrders table tr {
            color: var(--black1);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        .details .recentOrders table tr:last-child {
            border-bottom: none;
        }
        .details .recentOrders table tbody tr:hover {
            background: var(--blue);
            color: var(--white);
        }
        .details .recentOrders table tr td {
            padding: 10px;
        }
        .details .recentOrders table tr td:last-child {
            text-align: end;
        }
        .details .recentOrders table tr td:nth-child(2) {
            text-align: end;
        }
        .details .recentOrders table tr td:nth-child(3) {
            text-align: center;
        }
        .status.delivered {
            padding: 2px 4px;
            background: #8de02c;
            color: var(--white);
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.pending {
            padding: 2px 4px;
            background: #e9b10a;
            color: var(--white);
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.return {
            padding: 2px 4px;
            background: #f00;
            color: var(--white);
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.inProgress {
            padding: 2px 4px;
            background: #1795ce;
            color: var(--white);
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }

        .recentCustomers {
            position: relative;
            display: grid;
            min-height: 500px;
            padding: 20px;
            background: var(--white);
            box-shadow: 0 7px 25px rgba(0, 0, 0, 0.08);
            border-radius: 20px;
        }
        .recentCustomers .imgBx {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50px;
            overflow: hidden;
        }
        .recentCustomers .imgBx img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .recentCustomers table tr td {
            padding: 12px 10px;
        }
        .recentCustomers table tr td h4 {
            font-size: 16px;
            font-weight: 500;
            line-height: 1.2rem;
        }
        .recentCustomers table tr td h4 span {
            font-size: 14px;
            color: var(--black2);
        }
        .recentCustomers table tr:hover {
            background: var(--blue);
            color: var(--white);
        }
        .recentCustomers table tr:hover td h4 span {
            color: var(--white);
        }

        /* ====================== Responsive Design ========================== */
        @media (max-width: 991px) {
            .navigation {
                left: -300px;
            }
            .navigation.active {
                width: 300px;
                left: 0;
            }
            .main {
                width: 100%;
                left: 0;
                padding: 20px; /* Maintain padding */
            }
            .main.active {
                left: 300px;

            }
            .cardBox {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .details {
                grid-template-columns: 1fr;
            }
            .recentOrders {
                overflow-x: auto;
            }
            .status.inProgress {
                white-space: nowrap;
            }
        }

        @media (max-width: 480px) {
            .cardBox {
                grid-template-columns: repeat(1, 1fr);
            }
            .cardHeader h2 {
                font-size: 20px;
            }
            .user {
                min-width: 40px;
            }
            .navigation {
                width: 100%;
                left: -100%;
                z-index: 1000;
            }
            .navigation.active {
                width: 100%;
                left: 0;
            }
            .toggle {
                z-index: 10001;
            }
            .main.active .toggle {
                color: #fff;
                position: fixed;
                right: 0;
                left: initial;
            }
        }

        /* Add a class for the content area */
        .content-area {
            padding: 20px; /* Add some padding */
            background-color: #f8f9fa; /* Light background */
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1); /* Subtle shadow */
        }

    </style>
</head>

<body>
    <div class="container">
        <div class="navigation">
            <ul>
            <li>
    <a href="#">
        <span class="icon">
            <ion-icon name="football-outline"></ion-icon>  </span>
        <span class="title">Barberian Ramadhan Cup</span>
    </a>
</li>


                <li>
                    <a href="index.php">
                        <span class="icon">
                            <ion-icon name="home-outline"></ion-icon>
                        </span>
                        <span class="title">Dashboard</span>
                    </a>
                </li>

                <li>
                    <a href="teams.php">
                        <span class="icon">
                            <ion-icon name="people-outline"></ion-icon>
                        </span>
                        <span class="title">MANAGE TEAMS</span>
                    </a>
                </li>

                <li>
                   <a href="players.php">
                    <span class="icon">
                    <ion-icon name="people-outline"></ion-icon> </span>
                    <span class="title">Players</span>  </a>
                </li>

                <li>
    <a href="matches.php">
        <span class="icon">
            <ion-icon name="calendar-outline"></ion-icon>
        </span>
        <span class="title">Matches</span>
    </a>
</li>

<li>
    <a href="fixtures.php">
        <span class="icon">
            <ion-icon name="clipboard-outline"></ion-icon>
        </span>
        <span class="title">Fixtures</span>
    </a>
</li>

<li>
    <a href="Player_Stats.php">
        <span class="icon">
            <ion-icon name="stats-chart-outline"></ion-icon>
        </span>
        <span class="title">Player Stats</span>
    </a>
</li>

<li>
    <a href="create_group.php">
        <span class="icon">
            <ion-icon name="people-outline"></ion-icon>
        </span>
        <span class="title">Create Group</span> 
    </a>
</li>

<li>
    <a href="assign_teams.php">
        <span class="icon">
            <ion-icon name="person-add-outline"></ion-icon> 
        </span>
        <span class="title">Assing Team</span>
    </a>
</li>
<li>
    <a href="standings.php">
        <span class="icon">
            <ion-icon name="trophy-outline"></ion-icon> 
        </span>
        <span class="title">Standings</span> 
    </a>
</li>
<li>
    <a href="admin_overall_stats.php">
        <span class="icon">
            <ion-icon name="trophy-outline"></ion-icon> 
        </span>
        <span class="title">Overall Stats</span> 
    </a>
</li>
<li>
    <a href="sponsors.php">
        <span class="icon">
            <ion-icon name="business-outline"></ion-icon> 
        </span>
        <span class="title">Manage sponsors</span> 
    </a>
</li>
<li>
<a href="manage_highlights.php">
    <span class="icon">
        <ion-icon name="videocam-outline"></ion-icon>
    </span>
    <span class="title">Manage Highlights</span>
</a>
</li>
<li>
<a href="edit_event.php">
    <span class="icon">
        <ion-icon name="football-outline"></ion-icon>
    </span>
    <span class="title">Manage Events</span>
</a>
</li>
                <li>
                    <a href="logout.php">  <span class="icon">
                            <ion-icon name="log-out-outline"></ion-icon>
                        </span>
                        <span class="title">Sign Out</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="main">
            <div class="topbar">
                <div class="toggle">
                    <ion-icon name="menu-outline"></ion-icon>
                </div>

                <div class="search">
    <form action="search_results.php" method="GET">  <label>
            <input type="text" name="query" placeholder="Search here" required> <button type="submit" style="background: none; border: none; padding: 0; margin: 0; cursor: pointer;">
                <ion-icon name="search-outline"></ion-icon>
            </button>
        </label>
    </form>
</div>

                <div class="user">
                    <img src="assets/imgs/customer01.jpg" alt="">
                </div>
            </div>

            <div class="cardBox">
                <div class="card">
                <a href="leagues.php" class="text-decoration-none"> 
                <div>
                        <div class="numbers"><?php echo $conn->query("SELECT COUNT(*) FROM leagues")->fetch_row()[0]; ?></div>
                        <div class="cardName">Leagues</div> </a> 
                    </div>

                    <div class="iconBx">
                        <ion-icon name="trophy-outline"></ion-icon>
                    </div>
                </div>

                <div class="card">
                    <div>
                        <div class="numbers"><?php echo $conn->query("SELECT COUNT(*) FROM teams")->fetch_row()[0]; ?></div>
                        <div class="cardName">Teams</div>
                    </div>

                    <div class="iconBx">
                        <ion-icon name="people-outline"></ion-icon>
                    </div>
                </div>

                <div class="card">
                    <div>
                        <div class="numbers"><?php echo $conn->query("SELECT COUNT(*) FROM players")->fetch_row()[0]; ?></div>
                        <div class="cardName">Players</div>
                    </div>

                    <div class="iconBx">
                        <ion-icon name="person-outline"></ion-icon>
                    </div>
                </div>

                <div class="card">
                    <div>
                        <div class="numbers"><?php echo $conn->query("SELECT COUNT(*) FROM matches")->fetch_row()[0]; ?></div>
                        <div class="cardName">Matches</div>
                    </div>

                    <div class="iconBx">
                        <ion-icon name="calendar-outline"></ion-icon>
                    </div>
                </div>
            </div>

            <!-- ================ Order Details List ================= -->
            <div class="details">
                <div class="recentOrders">
                    <div class="cardHeader">
                        <h2>Recent Matches</h2>
                        <a href="matches.php" class="btn">View All</a>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <td>Home Team</td>
                                <td>Away Team</td>
                                <td>Date</td>
                                <td>Status</td>
                            </tr>
                        </thead>

                        <tbody>
                            <?php
                            $matches = $conn->query("SELECT matches.*, t1.name as home_team, t2.name as away_team 
                                                     FROM matches 
                                                     JOIN teams t1 ON matches.home_team_id = t1.id 
                                                     JOIN teams t2 ON matches.away_team_id = t2.id 
                                                     ORDER BY matches.date DESC LIMIT 5");
                            while ($row = $matches->fetch_assoc()) { ?>
                            <tr>
                                <td><?php echo $row['home_team']; ?></td>
                                <td><?php echo $row['away_team']; ?></td>
                                <td><?php echo date('d M Y H:i', strtotime($row['date'])); ?></td>
                                <td><span class="status <?php echo $row['status']; ?>"><?php echo ucfirst($row['status']); ?></span></td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>

                <div class="container mt-4">
    <div class="recentCustomers">
        <div class="cardHeader">
            <h2>Recent Players</h2>
        </div>

        <table>
            <?php
            // Fetch recent players and their team names (using prepared statement)
            $players_query = "SELECT players.*, teams.name as team_name
                              FROM players
                              JOIN teams ON players.team_id = teams.id
                              ORDER BY players.id DESC LIMIT 5";
            $players_stmt = $conn->prepare($players_query);

             if ($players_stmt === false) {
                die("Error preparing players query: " . $conn->error); // More specific error
            }

            $players_stmt->execute();
            $players_result = $players_stmt->get_result();



            if ($players_result && $players_result->num_rows > 0) :
                while ($row = $players_result->fetch_assoc()) :
                    ?>
                    <tr>
                        <td width="60px">
                            <div class="imgBx">
                                <?php if (!empty($row['image'])) : ?>
                                    <img src="../admin/uploads/<?php echo htmlspecialchars($row['image']); ?>" alt="Player" style="max-width: 100%; height: auto;">
                                <?php else : ?>
                                    <img src="../assets/imgs/default_player.jpg" alt="Default Player Image" style="max-width: 100%; height: auto;">
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <h4><?php echo htmlspecialchars($row['name']); ?> <br> <span><?php echo htmlspecialchars($row['team_name']); ?></span></h4>
                        </td>
                    </tr>
                <?php endwhile;
                 $players_stmt->close(); // Close the statement

             else : ?>
                <tr>
                    <td colspan="2">No recent players found.</td>
                </tr>

            <?php endif;
                $conn-> close();
            ?>
        </table>
    </div>
</div>
            <div class="content-area">
               <h1>Welcome, <?php echo $_SESSION['admin_username']; ?>!</h1>
                <p>This is your admin dashboard.</p>
                </div>

        </div>  </div>      <script>
        // add hovered class to selected list item
        let list = document.querySelectorAll(".navigation li");

        function activeLink() {
            list.forEach((item) => {
                item.classList.remove("hovered");
            });
            this.classList.add("hovered");
        }

        list.forEach((item) => item.addEventListener("mouseover", activeLink));

        // Menu Toggle
        let toggle = document.querySelector(".toggle");
        let navigation = document.querySelector(".navigation");
        let main = document.querySelector(".main");

        toggle.onclick = function () {
            navigation.classList.toggle("active");
            main.classList.toggle("active");
        };
    </script>

    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
</body>
</html>