<?php
include '../includes/db.php';
include '../includes/header.php';
include 'check_login.php';

// --- Goals Data ---
$goals_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_goals
                FROM players p
                INNER JOIN match_events me ON p.id = me.player_id
                INNER JOIN teams t ON p.team_id = t.id
                WHERE me.event_type = 'goal'
                GROUP BY p.id, p.name, t.name, p.image
                ORDER BY total_goals DESC";
$goals_result = $conn->query($goals_query);

// --- Assists Data ---
$assists_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_assists
                  FROM players p
                  INNER JOIN match_events me ON p.id = me.player_id
                  INNER JOIN teams t ON p.team_id = t.id
                  WHERE me.event_type = 'assist'
                  GROUP BY p.id, p.name, t.name, p.image
                  ORDER BY total_assists DESC";
$assists_result = $conn->query($assists_query);

// --- Clean Sheets Data ---
$clean_sheets_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_clean_sheets
                        FROM players p
                        INNER JOIN match_events me ON p.id = me.player_id
                        INNER JOIN teams t ON p.team_id = t.id
                        WHERE me.event_type = 'clean_sheet'
                        GROUP BY p.id, p.name, t.name, p.image
                        ORDER BY total_clean_sheets DESC";
$clean_sheets_result = $conn->query($clean_sheets_query);

// --- Yellow Cards Data ---
$yellow_cards_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_yellow_cards
                        FROM players p
                        INNER JOIN match_events me ON p.id = me.player_id
                        INNER JOIN teams t ON p.team_id = t.id
                        WHERE me.event_type = 'yellow_card'
                        GROUP BY p.id, p.name, t.name, p.image
                        ORDER BY total_yellow_cards DESC";
$yellow_cards_result = $conn->query($yellow_cards_query);

// --- Red Cards Data ---
$red_cards_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_red_cards
                    FROM players p
                    INNER JOIN match_events me ON p.id = me.player_id
                    INNER JOIN teams t ON p.team_id = t.id
                    WHERE me.event_type = 'red_card'
                    GROUP BY p.id, p.name, t.name, p.image
                    ORDER BY total_red_cards DESC";
$red_cards_result = $conn->query($red_cards_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <title>2024/25 Player Stats</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet"/>
    <style>
        body { font-family: 'Roboto', sans-serif; }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb; /* Line to differentiate players */
        }
        .stat-item h3 {
            margin-right: 1rem;
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .stat-item .stat-number {
            font-weight: bold;
        }
        .top-player {
            margin-bottom: 1rem;
        }
        .top-player-details {
            display: flex;
            align-items: center;
        }
        .player-image {
            margin-right: 10px;
        }

        /* New styles for specific cards */
        .goals-card .top-player-details,
        .goals-card .card-header{
          background-color: #0d0d66; /* Navy blue */
          color: white;
          padding: 0.5rem;
          border-radius: 0.25rem;
        }

        .assists-card .top-player-details,
        .assists-card .card-header {
          background-color: #c0c0c0; /* Silver */
          color: black;
            padding: 0.5rem;
          border-radius: 0.25rem;
        }

        .yellow-cards-card .top-player-details,
        .yellow-cards-card .card-header{
          background-color: #facc15; /* Yellow */
          color: black;
            padding: 0.5rem;
          border-radius: 0.25rem;
        }
        .clean-sheets-card .top-player-details,
        .clean-sheets-card .card-header{
          background-color: #10b981;
          color: white;
            padding: 0.5rem;
          border-radius: 0.25rem;
        }

        .red-cards-card .top-player-details,
        .red-cards-card .card-header{
          background-color: #ef4444; /* Red */
          color: white;
          padding: 0.5rem;
          border-radius: 0.25rem;
        }
        .card-header {
          margin-bottom: 1rem;
        }
         .player-info {
           display: flex;
           flex-direction: column;
        }
        /*Ensure consistency*/
        .bg-white {
            background-color: #fff;
        }


    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-purple-900 mb-6">2024/25 Player Stats</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

            <div class="goals-card bg-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold card-header">Goals</h2>
                <?php if ($goals_result && $goals_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $goals_result->fetch_assoc(); ?>
                    <div class="top-player">
                        <div class="top-player-details">
                            <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" width="50" height="50"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_goals']; ?></div>
                    </div>

                    <?php while ($row = $goals_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                          <div class="player-info">
                            <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                            <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                           </div>
                            <span class="stat-number"><?php echo $row['total_goals']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No goal data available.</p>
                <?php endif; ?>
                <?php $goals_result->data_seek(0); ?>
            </div>

            <div class="assists-card bg-white rounded-lg shadow p-4">
              <h2 class="text-xl font-bold card-header">Assists</h2>
                <?php if ($assists_result && $assists_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $assists_result->fetch_assoc(); ?>
                    <div class="top-player">
                        <div class="top-player-details">
                            <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" width="50" height="50"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_assists']; ?></div>
                    </div>

                    <?php while ($row = $assists_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                          <div class="player-info">
                            <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                            <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                           </div>
                            <span class="stat-number"><?php echo $row['total_assists']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No assist data available.</p>
                <?php endif; ?>
                <?php $assists_result->data_seek(0); ?>
            </div>

           <div class="clean-sheets-card bg-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold card-header">Clean Sheets</h2>
                <?php if ($clean_sheets_result && $clean_sheets_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $clean_sheets_result->fetch_assoc(); ?>
                    <div class="top-player">
                        <div class="top-player-details">
                            <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" width="50" height="50"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_clean_sheets']; ?></div>
                    </div>

                    <?php while ($row = $clean_sheets_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                            <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number"><?php echo $row['total_clean_sheets']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No clean sheet data available.</p>
                <?php endif; ?>
                <?php $clean_sheets_result->data_seek(0); ?>
            </div>
            <div class="yellow-cards-card bg-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold card-header">Yellow Cards</h2>
                <?php if ($yellow_cards_result && $yellow_cards_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $yellow_cards_result->fetch_assoc(); ?>
                    <div class="top-player">
                        <div class="top-player-details">
                            <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_yellow_cards']; ?></div>
                    </div>

                    <?php while ($row = $yellow_cards_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                            <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number"><?php echo $row['total_yellow_cards']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No yellow card data available.</p>
                <?php endif; ?>
                <?php $yellow_cards_result->data_seek(0); ?>
            </div>

             <div class="red-cards-card bg-white rounded-lg shadow p-4">
               <h2 class="text-xl font-bold card-header">Red Cards</h2>
               <?php if ($red_cards_result && $red_cards_result->num_rows > 0) : ?>
                   <?php $rank = 1; ?>
                   <?php $top_player = $red_cards_result->fetch_assoc(); ?>
                   <div class="top-player">
                       <div class="top-player-details">
                           <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>"/>
                           <div class="player-info">
                               <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                               <p class="text-sm text-gray-600"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                           </div>
                       </div>
                       <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_red_cards']; ?></div>
                       </div>

                    <?php while ($row = $red_cards_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                            <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number"><?php echo $row['total_red_cards']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No red card data available.</p>
                <?php endif; ?>
                <?php $red_cards_result->data_seek(0); ?>
            </div>
        </div>
    </div>
</body>
</html>
<?php $conn->close(); ?>








<--! YA ZAMANI -->$_COOKIE

<?php
include '../includes/db.php';
include '../includes/header.php';
include 'check_login.php';
// --- Goals Data ---
$goals_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_goals
                FROM players p
                INNER JOIN match_events me ON p.id = me.player_id
                INNER JOIN teams t ON p.team_id = t.id
                WHERE me.event_type = 'goal'
                GROUP BY p.id, p.name, t.name, p.image
                ORDER BY total_goals DESC";
$goals_result = $conn->query($goals_query);

// --- Assists Data ---
$assists_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_assists
                  FROM players p
                  INNER JOIN match_events me ON p.id = me.player_id
                  INNER JOIN teams t ON p.team_id = t.id
                  WHERE me.event_type = 'assist'
                  GROUP BY p.id, p.name, t.name, p.image
                  ORDER BY total_assists DESC";
$assists_result = $conn->query($assists_query);

// --- Clean Sheets Data ---
// --- Clean Sheets Data ---
$clean_sheets_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_clean_sheets
                       FROM players p
                       INNER JOIN match_events me ON p.id = me.player_id
                       INNER JOIN teams t ON p.team_id = t.id
                       WHERE me.event_type = 'clean_sheet'
                       GROUP BY p.id, p.name, t.name, p.image
                       ORDER BY total_clean_sheets DESC";
$clean_sheets_result = $conn->query($clean_sheets_query);

// --- Yellow Cards Data ---
$yellow_cards_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_yellow_cards
                        FROM players p
                        INNER JOIN match_events me ON p.id = me.player_id
                        INNER JOIN teams t ON p.team_id = t.id
                        WHERE me.event_type = 'yellow_card'
                        GROUP BY p.id, p.name, t.name, p.image
                        ORDER BY total_yellow_cards DESC";
$yellow_cards_result = $conn->query($yellow_cards_query);

// --- Red Cards Data ---
$red_cards_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_red_cards
                     FROM players p
                     INNER JOIN match_events me ON p.id = me.player_id
                     INNER JOIN teams t ON p.team_id = t.id
                     WHERE me.event_type = 'red_card'
                     GROUP BY p.id, p.name, t.name, p.image
                     ORDER BY total_red_cards DESC";
$red_cards_result = $conn->query($red_cards_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <title>2024/25 Player Stats</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet"/>
    <style>
        body { font-family: 'Roboto', sans-serif; }
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb; /* Line to differentiate players */
        }
        .stat-item h3 {
            margin-right: 1rem;
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .stat-item .stat-number {
            font-weight: bold;
        }
        .top-player {
            margin-bottom: 1rem;
        }
        .top-player-details {
            display: flex;
            align-items: center;
        }
        .player-image {
            margin-right: 10px;
        }
        .top-goal-player h3 {
            font-size: 1.5rem; /* Bigger font for top goal scorer */
        }
        .top-assist-player {
            background: linear-gradient(to right, #ef4444, #dc2626); /* Red gradient for top assist player */
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        .player-info {
            display: flex;
            flex-direction: column;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-purple-900 mb-6">2024/25 Player Stats</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

            <div class="bg-gradient-to-r from-red-500 to-red-700 text-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold mb-4">Goals</h2>
                <?php if ($goals_result && $goals_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $goals_result->fetch_assoc(); ?>
                    <div class="top-player top-goal-player">
                        <div class="top-player-details">
                            <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" width="50" height="50"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_goals']; ?></div>
                    </div>

                    <?php while ($row = $goals_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                            <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number text-white-200"><?php echo $row['total_goals']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No goal data available.</p>
                <?php endif; ?>
                <?php $goals_result->data_seek(0); ?>
            </div>

            <div class="bg-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold mb-4">Assists</h2>
                <?php if ($assists_result && $assists_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $assists_result->fetch_assoc(); ?>
                    <div class="top-player top-assist-player">
                        <div class="top-player-details">
                            <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" width="50" height="50"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold stat-number"><?php echo $top_player['total_assists']; ?></div>
                    </div>

                    <?php while ($row = $assists_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                            <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number text-gray-600"><?php echo $row['total_assists']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No assist data available.</p>
                <?php endif; ?>
                <?php $assists_result->data_seek(0); ?>
            </div>

            <div class="bg-white rounded-lg shadow p-4">
    <h2 class="text-xl font-bold mb-4">Clean Sheets</h2>
    <?php if ($clean_sheets_result && $clean_sheets_result->num_rows > 0) : ?>
        <?php $rank = 1; ?>
        <?php $top_player = $clean_sheets_result->fetch_assoc(); ?>
        <div class="top-player">
            <div class="top-player-details">
                <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" width="50" height="50"/>
                <div class="player-info">
                    <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                </div>
            </div>
            <div class="text-3xl font-bold text-red-600 stat-number"><?php echo $top_player['total_clean_sheets']; ?></div>
        </div>

        <?php while ($row = $clean_sheets_result->fetch_assoc()) : ?>
            <div class="stat-item">
                <div class="player-info">
                    <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($row['team_name']); ?></p>
                </div>
                <span class="text-number text-gray-600"><?php echo $row['total_clean_sheets']; ?></span>
            </div>
            <?php $rank++; ?>
        <?php endwhile; ?>
        <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
    <?php else : ?>
        <p>No clean sheet data available.</p>
    <?php endif; ?>
    <?php $clean_sheets_result->data_seek(0); ?>
</div>
            <div class="bg-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold mb-4">Yellow Cards</h2>
                <?php if ($yellow_cards_result && $yellow_cards_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $yellow_cards_result->fetch_assoc(); ?>
                     <div class="top-player">
                        <div class="top-player-details">
                           <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                         <div class="text-3xl font-bold text-yellow-600 stat-number"><?php echo $top_player['total_yellow_cards']; ?></div>
                    </div>

                    <?php while ($row = $yellow_cards_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                            <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number"><?php echo $row['total_yellow_cards']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No yellow card data available.</p>
                <?php endif; ?>
                <?php // Reset the data pointer back to the start if you need to loop again
                $yellow_cards_result->data_seek(0); ?>
            </div>

            <div class="bg-white rounded-lg shadow p-4">
                <h2 class="text-xl font-bold mb-4">Red Cards</h2>
                <?php if ($red_cards_result && $red_cards_result->num_rows > 0) : ?>
                    <?php $rank = 1; ?>
                    <?php $top_player = $red_cards_result->fetch_assoc(); ?>
                    <div class="top-player">
                        <div class="top-player-details">
                           <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="w-12 h-12 rounded-full mr-4 player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>"/>
                            <div class="player-info">
                                <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                            </div>
                        </div>
                        <div class="text-3xl font-bold text-red-600 stat-number"><?php echo $top_player['total_red_cards']; ?></div>
                    </div>

                    <?php while ($row = $red_cards_result->fetch_assoc()) : ?>
                        <div class="stat-item">
                           <div class="player-info">
                                <h3><?php echo $rank + 1 . '. ' . htmlspecialchars($row['name']); ?></h3>
                                <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                            </div>
                            <span class="stat-number"><?php echo $row['total_red_cards']; ?></span>
                        </div>
                        <?php $rank++; ?>
                    <?php endwhile; ?>
                    <button class="mt-4 bg-gray-200 text-gray-800 py-2 px-4 rounded-full">View Full List <i class="fas fa-arrow-right"></i></button>
                <?php else : ?>
                    <p>No red card data available.</p>
                    <?php endif; ?>
                <?php $red_cards_result->data_seek(0); ?>
            </div>
        </div>
    </div>
</body>
</html>
<?php $conn->close(); ?>