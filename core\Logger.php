<?php
class Logger {
    private static function log($message, $type = 'error') {
        $logFile = $type === 'error' ? ERROR_LOG : ACCESS_LOG;
        $timestamp = date('[Y-m-d H:i:s]');
        $logMessage = sprintf("%s %s: %s%s", $timestamp, strtoupper($type), $message, PHP_EOL);
        
        return error_log($logMessage, 3, $logFile);
    }
    
    public static function error($message) {
        return self::log($message, 'error');
    }
    
    public static function access($message) {
        return self::log($message, 'access');
    }
}
