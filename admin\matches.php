<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Create Match
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_match'])) {
    $league_id = $_POST['league_id'];
    $home_team_id = $_POST['home_team_id'];
    $away_team_id = $_POST['away_team_id'];
    $date = $_POST['date'];
    $status = $_POST['status'];

    $sql = "INSERT INTO matches (league_id, home_team_id, away_team_id, date, status) 
            VALUES ('$league_id', '$home_team_id', '$away_team_id', '$date', '$status')";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Match created successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Fetch Matches
$matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                        FROM matches 
                        JOIN leagues ON matches.league_id = leagues.id 
                        JOIN teams t1 ON matches.home_team_id = t1.id 
                        JOIN teams t2 ON matches.away_team_id = t2.id");

$live_matches = $conn->query("SELECT matches.*, leagues.name as league_name, 
                        t1.name as home_team, t2.name as away_team 
                        FROM matches 
                        JOIN leagues ON matches.league_id = leagues.id 
                        JOIN teams t1 ON matches.home_team_id = t1.id 
                        JOIN teams t2 ON matches.away_team_id = t2.id 
                        WHERE matches.status = 'live'
                        ORDER BY matches.date DESC");

$leagues = $conn->query("SELECT * FROM leagues");
$teams_list = [];  // Use a single array for all teams
$teams = $conn->query("SELECT * FROM teams");
while ($row = $teams->fetch_assoc()) {
    $teams_list[] = $row; // Add each team to the array
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Management</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .sidebar { background-color: #fff; padding: 20px; border-radius: 10px; }
        .match-card { background: #ffffff; border-radius: 8px; padding: 10px; margin-bottom: 10px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
        .live { color: red; font-weight: bold; }
        .finished { color: green; }
        .upcoming { color: blue; }
        .vs-circle { width: 30px; height: 30px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; font-size: 0.8em; color: #343a40; border: 1px solid #dee2e6;}
    </style>
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">Manage Matches</h1>
    <div class="card p-4 shadow-sm">
        <form method="POST">
            <div class="mb-3">
                <label class="form-label">League</label>
                <select name="league_id" class="form-control" required>
                    <option value="">Select League</option>
                    <?php while ($row = $leagues->fetch_assoc()) : ?>
                        <option value="<?php echo $row['id']; ?>"><?php echo $row['name']; ?></option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Home Team</label>
                    <select name="home_team_id" class="form-control" required>
                        <option value="">Select Home Team</option>
                        <?php foreach ($teams_list as $team) : ?>
                            <option value="<?php echo $team['id']; ?>"><?php echo $team['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Away Team</label>
                    <select name="away_team_id" class="form-control" required>
                        <option value="">Select Away Team</option>
                        <?php foreach ($teams_list as $team) : ?>
                            <option value="<?php echo $team['id']; ?>"><?php echo $team['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">Match Date & Time</label>
                <input type="datetime-local" name="date" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-control" required>
                    <option value="upcoming">Upcoming</option>
                    <option value="live">Live</option>
                    <option value="finished">Finished</option>
                </select>
            </div>
            <button type="submit" name="create_match" class="btn btn-primary w-100">Create Match</button>
        </form>
    </div>

    <h2 class="text-center mt-5">All Matches</h2>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead class="table-dark">
                <tr>
                    <th>ID</th>
                    <th>League</th>
                    <th>Home Team</th>
                    <th>Away Team</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                    <th>Lineup</th>
                </tr>
            </thead>
            <!-- ... other parts of your code remain unchanged ... -->
<tbody>
    <?php while ($row = $matches->fetch_assoc()) : ?>
    <tr>
        <td><?php echo $row['id']; ?></td>
        <td><?php echo $row['league_name']; ?></td>
        <td><?php echo $row['home_team']; ?></td>
        <td><?php echo $row['away_team']; ?></td>
        <td><?php echo $row['date']; ?></td>
        <td><?php echo ucfirst($row['status']); ?></td>
        <td>
            <a href="edit_match.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
            <a href="delete_match.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</a>
        </td>
        <td>
            <!-- Pass the match id to the lineup page -->
            <a href="lineup.php?match_id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">Lineup</a>
        </td>
    </tr>
    <?php endwhile; ?>
</tbody>

        </table>
    </div>
</div>

<h2 class="text-center mt-5">Live Scoreboard</h2>
<div class="row justify-content-center">
    <?php if ($live_matches->num_rows > 0) : ?>
        <?php while ($match = $live_matches->fetch_assoc()) :
            $match_time = strtotime($match['date']);
            $current_time = time();
            $minutes_elapsed = min(90, floor(($current_time - $match_time) / 60));

            // Fetch players *only once* per match, outside the event listeners
            $home_players_result = $conn->query("SELECT * FROM players WHERE team_id = {$match['home_team_id']}");
            $away_players_result = $conn->query("SELECT * FROM players WHERE team_id = {$match['away_team_id']}");

            // Build associative arrays of players (ID => Name)
            $home_players = [];
            while ($player = $home_players_result->fetch_assoc()) {
                $home_players[$player['id']] = $player['name'];
            }
            $away_players = [];
            while ($player = $away_players_result->fetch_assoc()) {
                $away_players[$player['id']] = $player['name'];
            }
        ?>
            <div class="col-md-4 mb-3">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-danger text-white py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small fw-bold"><i class="fas fa-trophy me-1"></i><?php echo $match['league_name']; ?></span>
                            <div>
                                <span class="match-timer badge bg-danger-subtle text-danger border border-danger me-1" data-start="<?php echo $match_time; ?>">
                                     <?php echo $minutes_elapsed; ?>'
                                </span>
                                <span class="badge bg-light text-danger"><i class="fas fa-circle-dot fa-beat"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body py-2 px-3">
                        <div class="row align-items-center text-center g-0">
                            <div class="col-5">
                                <div class="team-name small mb-1"><?php echo $match['home_team']; ?></div>
                                <input type="number" id="home-score-<?php echo $match['id']; ?>" value="<?php echo $match['home_team_score']; ?>" class="score-input" />
                            </div>
                            <div class="col-2">
                                <div class="vs-circle">vs</div>
                            </div>
                            <div class="col-5">
                                <div class="team-name small mb-1"><?php echo $match['away_team']; ?></div>
                                <input type="number" id="away-score-<?php echo $match['id']; ?>" value="<?php echo $match['away_team_score']; ?>" class="score-input" />
                            </div>
                        </div>

                        <div class="mt-2">
                            <select id="status-<?php echo $match['id']; ?>" class="form-select">
                                <option value="live" <?php echo $match['status'] == 'live' ? 'selected' : ''; ?>>Live</option>
                                <option value="finished" <?php echo $match['status'] == 'finished' ? 'selected' : ''; ?>>Finished</option>
                                <option value="upcoming" <?php echo $match['status'] == 'upcoming' ? 'selected' : ''; ?>>Upcoming</option>
                            </select>
                        </div>
                        <button class="btn btn-primary update-score mt-2" data-match-id="<?php echo $match['id']; ?>">Update Score</button>

                        <div class="mt-3">
                            <h5>Add Match Event</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="event-type-<?php echo $match['id']; ?>" class="form-label">Event Type:</label>
                                    <select class="form-select" id="event-type-<?php echo $match['id']; ?>">
                                        <option value="goal">Goal</option>
                                        <option value="assist">Assist</option>
                                        <option value="yellow_card">Yellow Card</option>
                                        <option value="red_card">Red Card</option>
                                        <option value="penalty">Penalty</option>
                                        <option value="clean_sheet">Clean Sheet</option>
                                        <!-- New Event Types -->
                                        <option value="first_half">First Half</option>
                                        <option value="half_time">Half Time</option>
                                        <option value="second_half">Second Half</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="event-minute-<?php echo $match['id']; ?>" class="form-label">Minute:</label>
                                    <input type="number" class="form-control" id="event-minute-<?php echo $match['id']; ?>" min="0" max="120">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="team-select-<?php echo $match['id']; ?>" class="form-label">Team:</label>
                                    <select class="form-select team-select" id="team-select-<?php echo $match['id']; ?>" data-match-id="<?php echo $match['id']; ?>">
                                        <option value="<?php echo $match['home_team_id']; ?>"><?php echo $match['home_team']; ?></option>
                                        <option value="<?php echo $match['away_team_id']; ?>"><?php echo $match['away_team']; ?></option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="player-select-<?php echo $match['id']; ?>" class="form-label">Player:</label>
                                    <select class="form-select player-select" id="player-select-<?php echo $match['id']; ?>" data-match-id="<?php echo $match['id']; ?>">
                                        <option value="">Select Player</option>
                                    </select>
                                </div>
                            </div>
                            <button class="btn btn-success mt-2 add-event" data-match-id="<?php echo $match['id']; ?>">Add Event</button>
                        </div>

                        <div class="mt-3">
                            <h5>Match Events</h5>
                            <table class="table table-sm table-bordered" id="event-table-<?php echo $match['id']; ?>">
                                <thead>
                                    <tr>
                                        <th>Minute</th>
                                        <th>Team</th>
                                        <th>Event</th>
                                        <th>Player</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    <?php else : ?>
        <div class="col-12 text-center">
            <div class="alert alert-info py-2 small">
                <i class="fas fa-info-circle me-1"></i>No live matches
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Timer update
    const timers = document.querySelectorAll('.match-timer');
    function updateTimers() {
        timers.forEach(timer => {
            const startTime = parseInt(timer.dataset.start);
            const currentTime = Math.floor(Date.now() / 1000);
            let minutes = Math.floor((currentTime - startTime) / 60);
            minutes = Math.min(90, minutes);
            timer.textContent = minutes + "'";
        });
    }
    setInterval(updateTimers, 60000);

    // Update score
    document.querySelectorAll('.update-score').forEach(button => {
        button.addEventListener('click', function() {
            const matchId = this.getAttribute('data-match-id');
            const homeScore = document.getElementById('home-score-' + matchId).value;
            const awayScore = document.getElementById('away-score-' + matchId).value;
            const status = document.getElementById('status-' + matchId).value;

            fetch('update_score.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ match_id: matchId, home_team_score: homeScore, away_team_score: awayScore, status: status }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Score updated successfully!');
                    location.reload(); // Reload the page
                } else {
                    alert('Error updating score: ' + data.message);
                }
            })
            .catch(error => console.error('Error:', error));
        });
    });

    // Function to load and display match events
    function loadMatchEvents(matchId, tableElement) {
        fetch('get_match_events.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ match_id: matchId })
        })
        .then(response => response.json())
        .then(events => {
            tableElement.querySelector('tbody').innerHTML = ''; // Clear existing rows
            events.forEach(event => {
                const row = tableElement.querySelector('tbody').insertRow();
                row.insertCell(0).textContent = event.minute + "'";
                row.insertCell(1).textContent = event.team_name;
                row.insertCell(2).textContent = event.event_type;
                row.insertCell(3).textContent = event.player_name || '';
            });
        })
        .catch(error => console.error('Error:', error));
    }

    // Function to populate player dropdown
    function populatePlayerDropdown(matchId) {
        const teamSelect = document.getElementById(`team-select-${matchId}`);
        const playerSelect = document.getElementById(`player-select-${matchId}`);
        const selectedTeamId = teamSelect.value;

        playerSelect.innerHTML = '<option value="">Select Player</option>'; // Clear

        if (selectedTeamId) {
            const players = (selectedTeamId == teamSelect.options[0].value) ? <?php echo json_encode($home_players); ?> : <?php echo json_encode($away_players); ?>;

            for (const playerId in players) {
                if (players.hasOwnProperty(playerId)) {
                    const option = document.createElement('option');
                    option.value = playerId;
                    option.textContent = players[playerId];
                    playerSelect.appendChild(option);
                }
            }
        }
    }

    // Event listener for team selection change
    document.querySelectorAll('.team-select').forEach(select => {
        select.addEventListener('change', function() {
            const matchId = this.dataset.matchId;
            populatePlayerDropdown(matchId); // Populate based on selected team
        });
    });

    // Event listener for adding a new match event
    document.querySelectorAll('.add-event').forEach(button => {
        button.addEventListener('click', function() {
            const matchId = this.dataset.matchId;
            const eventType = document.getElementById('event-type-' + matchId).value;
            const minute = document.getElementById('event-minute-' + matchId).value;
            const teamId = document.getElementById('team-select-' + matchId).value;
            const playerId = document.getElementById('player-select-' + matchId).value;
            const eventTable = document.getElementById('event-table-' + matchId);

            // Client-Side Validation
            const playerRequiredEvents = ['goal', 'assist', 'yellow_card', 'red_card', 'clean_sheet'];
            if (playerRequiredEvents.includes(eventType) && !playerId) {
                alert('Please select a player for this event type.');
                return; // Stop if validation fails
            }

            // AJAX Request
            fetch('add_match_event.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    match_id: matchId,
                    event_type: eventType,
                    team_id: teamId,
                    player_id: playerId,
                    minute: minute
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Event added successfully!');
                    loadMatchEvents(matchId, eventTable); // Reload events
                } else {
                    alert('Error adding event: ' + data.message);
                }
            })
            .catch(error => console.error('Error:', error));
        });
    });

    // Load match events and populate initial player dropdowns on page load
    document.querySelectorAll('.match-card').forEach(card => {
        const matchId = card.querySelector('.update-score').dataset.matchId;
        const eventTable = document.getElementById('event-table-' + matchId);
        if (matchId && eventTable) {
            loadMatchEvents(matchId, eventTable);  // Load existing events
            populatePlayerDropdown(matchId); // Populate on initial load
        }
    });
});
</script>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-3 sidebar">
            <h5>Top Tournaments</h5>
            <ul class="list-group">
                <li class="list-group-item">Ramadan Cup</li>
            </ul>
        </div>
        <div class="col-md-9">
            <h2>Today's Matches</h2>
            <div class="row">
                <?php
                $matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                                        FROM matches 
                                        JOIN leagues ON matches.league_id = leagues.id 
                                        JOIN teams t1 ON matches.home_team_id = t1.id 
                                        JOIN teams t2 ON matches.away_team_id = t2.id");
                while ($row = $matches->fetch_assoc()) : ?>
                    <div class="col-md-6">
                        <div class="match-card">
                            <div><strong><?php echo $row['league_name']; ?></strong></div>
                            <div>
                                <span><?php echo $row['home_team']; ?></span>
                                <strong>VS</strong>
                                <span><?php echo $row['away_team']; ?></span>
                            </div>
                            <div>
                                <small><?php echo $row['date']; ?></small>
                            </div>
                            <div>
                                <span class="<?php echo $row['status']; ?>">
                                    <?php echo ucfirst($row['status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</div>

</body>
</html>
<?php include '../includes/footer.php'; ?>