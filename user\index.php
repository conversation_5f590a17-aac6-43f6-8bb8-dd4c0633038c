<?php
include '../includes/db.php';
include '../includes/header2.php';

// Fetch Matches
$matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                         FROM matches 
                         JOIN leagues ON matches.league_id = leagues.id 
                         JOIN teams t1 ON matches.home_team_id = t1.id 
                         JOIN teams t2 ON matches.away_team_id = t2.id 
                         ORDER BY matches.date ASC");
?>

<!-- Page Title -->
<div class="container mt-4">
    <h1 class="text-center mb-4">Football Matches</h1>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="matchesTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">All Matches</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="live-tab" data-bs-toggle="tab" data-bs-target="#live" type="button" role="tab">Live</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="upcoming-tab" data-bs-toggle="tab" data-bs-target="#upcoming" type="button" role="tab">Upcoming</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="finished-tab" data-bs-toggle="tab" data-bs-target="#finished" type="button" role="tab">Finished</button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="matchesTabContent">
        <!-- All Matches -->
        <div class="tab-content" id="matchesTabContent">
        <div class="tab-pane fade show active" id="all" role="tabpanel">
            <div class="row">
                <?php while ($row = $matches->fetch_assoc()) { ?>
                    <div class="col-md-6 mb-4">
                        <a href="match_details.php?match_id=<?php echo $row['id']; ?>" class="card-link">  <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?></h5>
                                    <h6 class="card-subtitle mb-2 text-muted"><?php echo $row['league_name']; ?></h6>
                                    <p class="card-text">
                                        <strong>Date:</strong> <?php echo date('d M Y H:i', strtotime($row['date'])); ?><br>
                                        <strong>Status:</strong> <?php echo ucfirst($row['status']); ?>
                                    </p>
                                    <?php if ($row['status'] == 'live') { ?>
                                        <p class="card-text text-danger">Live: <?php echo $row['minute']; ?>'</p>
                                    <?php } ?>
                                    <p class="card-text">
                                        <strong>Score:</strong> <?php echo $row['home_team_score']; ?> - <?php echo $row['away_team_score']; ?>
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php } ?>
            </div>
        </div>

        <!-- Live Matches -->
        <div class="tab-pane fade" id="live" role="tabpanel">
            <div class="row">
                <?php
                $live_matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                                              FROM matches 
                                              JOIN leagues ON matches.league_id = leagues.id 
                                              JOIN teams t1 ON matches.home_team_id = t1.id 
                                              JOIN teams t2 ON matches.away_team_id = t2.id 
                                              WHERE matches.status = 'live' 
                                              ORDER BY matches.date ASC");
                while ($row = $live_matches->fetch_assoc()) { ?>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?></h5>
                            <h6 class="card-subtitle mb-2 text-muted"><?php echo $row['league_name']; ?></h6>
                            <p class="card-text">
                                <strong>Date:</strong> <?php echo date('d M Y H:i', strtotime($row['date'])); ?><br>
                                <strong>Status:</strong> <?php echo ucfirst($row['status']); ?>
                            </p>
                            <p class="card-text text-danger">Live: <?php echo $row['minute']; ?>'</p>
                            <p class="card-text">
                                <strong>Score:</strong> <?php echo $row['home_team_score']; ?> - <?php echo $row['away_team_score']; ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>

        <!-- Upcoming Matches -->
        <div class="tab-pane fade" id="upcoming" role="tabpanel">
            
            <div class="row">
                <?php
                $upcoming_matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                                                  FROM matches 
                                                  JOIN leagues ON matches.league_id = leagues.id 
                                                  JOIN teams t1 ON matches.home_team_id = t1.id 
                                                  JOIN teams t2 ON matches.away_team_id = t2.id 
                                                  WHERE matches.status = 'upcoming' 
                                                  ORDER BY matches.date ASC");
                while ($row = $upcoming_matches->fetch_assoc()) { ?>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?></h5>
                            <h6 class="card-subtitle mb-2 text-muted"><?php echo $row['league_name']; ?></h6>
                            <p class="card-text">
                                <strong>Date:</strong> <?php echo date('d M Y H:i', strtotime($row['date'])); ?><br>
                                <strong>Status:</strong> <?php echo ucfirst($row['status']); ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>

        <!-- Finished Matches -->
        <div class="tab-pane fade" id="finished" role="tabpanel">
            <div class="row">
                <?php
                $finished_matches = $conn->query("SELECT matches.*, leagues.name as league_name, t1.name as home_team, t2.name as away_team 
                                                  FROM matches 
                                                  JOIN leagues ON matches.league_id = leagues.id 
                                                  JOIN teams t1 ON matches.home_team_id = t1.id 
                                                  JOIN teams t2 ON matches.away_team_id = t2.id 
                                                  WHERE matches.status = 'finished' 
                                                  ORDER BY matches.date DESC");
                while ($row = $finished_matches->fetch_assoc()) { ?>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?></h5>
                            <h6 class="card-subtitle mb-2 text-muted"><?php echo $row['league_name']; ?></h6>
                            <p class="card-text">
                                <strong>Date:</strong> <?php echo date('d M Y H:i', strtotime($row['date'])); ?><br>
                                <strong>Status:</strong> <?php echo ucfirst($row['status']); ?>
                            </p>
                            <p class="card-text">
                                <strong>Score:</strong> <?php echo $row['home_team_score']; ?> - <?php echo $row['away_team_score']; ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>