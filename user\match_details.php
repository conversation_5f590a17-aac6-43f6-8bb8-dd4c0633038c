<?php
include '../includes/db.php';

// Check if match_id is provided
if (!isset($_GET['match_id'])) {
    echo "Error: Match ID is missing.";
    exit;
}

$match_id = mysqli_real_escape_string($conn, $_GET['match_id']);

// Fetch match details including scores
$match_details_query = "SELECT md.*,
                            m.date,
                            m.home_team_score,
                            m.away_team_score,
                            m.status,
                            m.minute,
                            t1.name AS home_team,
                            t2.name AS away_team,
                            l.name AS league_name,
                            t1.logo AS home_logo,  -- Get team logos
                            t2.logo AS away_logo
                        FROM match_details md
                        JOIN matches m ON md.match_id = m.id
                        JOIN teams t1 ON m.home_team_id = t1.id
                        JOIN teams t2 ON m.away_team_id = t2.id
                        JOIN leagues l ON m.league_id = l.id
                        WHERE md.match_id = '$match_id'";

$match_details_result = $conn->query($match_details_query);

if (!$match_details_result) {
    echo "Error executing query: ". $conn->error;
    exit;
}

if ($match_details_result->num_rows > 0) {
    $match_details = $match_details_result->fetch_assoc();
} else {
    echo "Match details not found.";
    exit;
}

// Function to display players
function displayPlayers($playerString) {
    if (empty($playerString)) {
        return "No players listed.";
    }

    $players = explode(',', $playerString);
    $output = '<div class="player-list">';
    for ($i = 0; $i < count($players); $i += 2) {
        $name = $players[$i]?? 'Unknown';
        $position = $players[$i + 1]?? 'Unknown';
        $output.= '<div class="player-item">
                        <span class="player-name">'. htmlspecialchars($name). '</span>
                        <span class="player-position">'. htmlspecialchars($position). '</span>
                    </div>';
    }
    $output.= '</div>';
    return $output;
}?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Match Details</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined|Material+Icons" rel="stylesheet" />
    <link rel="stylesheet" href="https://public.codepenassets.com/css/reset-2.0.min.css">
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
*,
*:after,
*:before {
  box-sizing: border-box;
}:root {
  --color-text-primary: #1c2a38;
  --color-text-secondary: #8A8F98;
  --color-text-alert: #d72641;
  --color-text-icon: #dbdade;
  --color-bg-primary: #fff;
  --color-bg-secondary: #f3f5f9;
  --color-bg-alert: #fdeaec;
  --color-theme-primary: #623ce6;
}

button,
input,
select,
textarea {
  font: inherit;
}

img {
  display: block;
}

strong {
  font-weight: 600;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
}.match {
  background-color: var(--color-bg-primary);
  display: flex;
  flex-direction: column;
  min-width: 600px;
  border-radius: 10px;
  box-shadow: 0 0 2px 0 rgba(48, 48, 48, 0.1), 0 4px 4px 0 rgba(48, 48, 48, 0.1);
}.match-header {
  display: flex;
  padding: 16px;
  border-bottom: 2px solid rgba(48, 48, 48, 0.1);
}.match-status {
  background-color: var(--color-bg-alert);
  color: var(--color-text-alert);
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  line-height: 1;
  margin-right: auto;
}.match-status:before {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  background-color: currentcolor;
  border-radius: 50%;
  margin-right: 8px;
}.match-tournament {
  display: flex;
  align-items: center;
  font-weight: 600;
}.match-tournament img {
  width: 20px;
  margin-right: 12px;
}.match-actions {
  display: flex;
  margin-left: auto;
}.btn-icon {
  border: none;
  background-color: transparent;
  color: var(--color-text-icon);
  display: flex;
  align-items: center;
  justify-content: center;
}.match-content {
  display: flex;
  position: relative;
}.column {
  padding: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100%/3);
}.team {
  display: flex;
  flex-direction: column;
  align-items: center;
}.team-logo {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-bg-primary);
  box-shadow: 0 4px 4px 0 rgba(48, 48, 48, 0.15), 0 0 0 15px var(--color-bg-secondary);
}.team-logo img {
  width: 50px;
}.team-name {
  text-align: center;
  margin-top: 24px;
  font-size: 20px;
  font-weight: 600;
}.match-details {
  display: flex;
  flex-direction: column;
  align-items: center;
}.match-date,.match-referee {
  font-size: 14px;
  color: var(--color-text-secondary);
}.match-date strong,.match-referee strong {
  color: var(--color-text-primary);
}.match-score {
  margin-top: 12px;
  display: flex;
  align-items: center;
}.match-score-number {
  font-size: 48px;
  font-weight: 600;
  line-height: 1;
}.match-score-number--leading {
  color: var(--color-theme-primary);
}.match-score-divider {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  color: var(--color-text-icon);
  margin-left: 10px;
  margin-right: 10px;
}.match-time-lapsed {
  color: #DF9443;
  font-size: 14px;
  font-weight: 600;
  margin-top: 8px;
}.match-referee {
  margin-top: 12px;
}.match-bet-options {
  display: flex;
  margin-top: 8px;
  padding-bottom: 12px;
}.match-bet-option {
  margin-left: 4px;
  margin-right: 4px;
  border: 1px solid var(--color-text-icon);
  background-color: #F9F9F9;
  border-radius: 2px;
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
}.match-bet-place {
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  border: none;
  background-color: var(--color-theme-primary);
  border-radius: 6px;
  padding: 10px 48px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  box-shadow: 0 4px 8px 0 rgba(48, 48, 48, 0.25);
}.container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* New styles for lineup */.lineup {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
}.lineup-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    width: 45%;
}.player-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}.player-item {
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
    </style>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

<div class="container">
    <div class="match">
        <div class="match-header">
            <div class="match-status"><?php echo ucfirst(htmlspecialchars($match_details['status']));?></div>
            <div class="match-tournament">
                <img src="path/to/your/league/logo.svg" alt="<?php echo htmlspecialchars($match_details['league_name']);?> Logo" />
                <?php echo htmlspecialchars($match_details['league_name']);?>
            </div>
            <div class="match-actions">
                <button class="btn-icon"><i class="material-icons-outlined">grade</i></button>
                <button class="btn-icon"><i class="material-icons-outlined">add_alert</i></button>
            </div>
        </div>

        <div class="match-content">
            <div class="column">
                <div class="team team--home">
                    <div class="team-logo">
                        <img src="<?php echo htmlspecialchars($match_details['home_logo']);?>" alt="<?php echo htmlspecialchars($match_details['home_team']);?> Logo">
                    </div>
                    <h2 class="team-name"><?php echo htmlspecialchars($match_details['home_team']);?></h2>
                </div>
            </div>

            <div class="column">
                <div class="match-details">
                    <div class="match-date">
                        <?php echo date('d M', strtotime($match_details['date']));?> at <strong><?php echo date('H:i', strtotime($match_details['date']));?></strong>
                    </div>
                    <div class="match-score">
                        <span class="match-score-number match-score-number--leading"><?php echo isset($match_details['home_team_score'])? $match_details['home_team_score']: 'N/A';?></span>
                        <span class="match-score-divider">:</span>
                        <span class="match-score-number"><?php echo isset($match_details['away_team_score'])? $match_details['away_team_score']: 'N/A';?></span>
                    </div>
                    <?php if ($match_details['status'] == 'live') {?>
                        <div class="match-time-lapsed">
                            <?php echo htmlspecialchars($match_details['minute']);?>'
                        </div>
                    <?php }?>
                    <div class="match-referee">
                        Referee: <strong>Mike Dean</strong>
                    </div>
                    </div>
            </div>

            <div class="column">
                <div class="team team--away">
                    <div class="team-logo">
                        <img src="<?php echo htmlspecialchars($match_details['away_logo']);?>" alt="<?php echo htmlspecialchars($match_details['away_team']);?> Logo">
                    </div>
                    <h2 class="team-name"><?php echo htmlspecialchars($match_details['away_team']);?></h2>
                </div>
            </div>
        </div>

        <div class="lineup">
            <div class="lineup-section">
                <div class="lineup-header">Home Team Lineup</div>
                <?php echo displayPlayers($match_details['home_starting']?? '');?>
                <div class="lineup-header">Substitutes</div>
                <?php echo displayPlayers($match_details['home_subs']?? '');?>
            </div>

            <div class="lineup-section">
                <div class="lineup-header">Away Team Lineup</div>
                <?php echo displayPlayers($match_details['away_starting']?? '');?>
                <div class="lineup-header">Substitutes</div>
                <?php echo displayPlayers($match_details['away_subs']?? '');?>
            </div>
        </div>

    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>