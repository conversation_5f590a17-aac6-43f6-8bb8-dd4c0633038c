<?php
require_once 'db.php';
header('Content-Type: application/json');

// For direct Flutterwave webhook calls
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_SERVER['HTTP_VERIF_HASH'])) {
    handleWebhook();
    exit;
}

// For frontend verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate CSRF token
    if (empty($input['csrf_token']) || $input['csrf_token'] !== $_SESSION['csrf_token']) {
        http_response_code(403);
        echo json_encode(['error' => 'Invalid CSRF token']);
        exit;
    }
    
    // Verify payment with Flutterwave
    $transactionId = $input['transaction_id'];
    $txRef = $input['tx_ref'];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.flutterwave.com/v3/transactions/$transactionId/verify");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . FLW_SECRET_KEY
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo json_encode(['status' => 'error', 'message' => 'Payment verification failed']);
        exit;
    }
    
    $responseData = json_decode($response, true);
    
    // Check if payment was successful
    if ($responseData['status'] === 'success' && 
        $responseData['data']['status'] === 'successful' && 
        $responseData['data']['tx_ref'] === $txRef) {
        
        // Update transaction in database
        try {
            $stmt = $db->prepare("UPDATE transactions SET 
                status = 'completed',
                flutterwave_id = ?,
                payment_method = ?,
                updated_at = NOW()
                WHERE tx_ref = ?");
            $stmt->execute([
                $responseData['data']['id'],
                $responseData['data']['payment_type'],
                $txRef
            ]);
        } catch(PDOException $e) {
            error_log("Database error: " . $e->getMessage());
        }
        
        echo json_encode([
            'status' => 'success',
            'tx_ref' => $txRef,
            'amount' => $responseData['data']['amount'],
            'currency' => $responseData['data']['currency']
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Payment not successful'
        ]);
    }
    exit;
}

function handleWebhook() {
    global $db;
    
    $secretHash = FLW_WEBHOOK_HASH; // Set this in your config.php
    $signature = $_SERVER['HTTP_VERIF_HASH'];
    
    if ($signature !== $secretHash) {
        http_response_code(401);
        die();
    }
    
    $payload = json_decode(file_get_contents('php://input'), true);
    
    // Verify the transaction
    $transactionId = $payload['data']['id'];
    $txRef = $payload['data']['tx_ref'];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.flutterwave.com/v3/transactions/$transactionId/verify");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . FLW_SECRET_KEY
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $responseData = json_decode($response, true);
        
        // Update transaction status
        try {
            $stmt = $db->prepare("UPDATE transactions SET 
                status = ?,
                flutterwave_id = ?,
                payment_method = ?,
                updated_at = NOW()
                WHERE tx_ref = ?");
            $stmt->execute([
                $responseData['data']['status'],
                $responseData['data']['id'],
                $responseData['data']['payment_type'],
                $txRef
            ]);
        } catch(PDOException $e) {
            error_log("Database error: " . $e->getMessage());
        }
    }
    
    http_response_code(200);
    echo json_encode(['status' => 'success']);
}
?>