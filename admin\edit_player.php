<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';
if (!isset($_GET['id'])) {
    header("Location: players.php");
    exit();
}

$id = $_GET['id'];

// Fetch Player Details
$player = $conn->query("SELECT * FROM players WHERE id = $id")->fetch_assoc();
$teams = $conn->query("SELECT * FROM teams");

// Update Player
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_player'])) {
    $name = $_POST['name'];
    $team_id = $_POST['team_id'];
    $position = $_POST['position'];
    $jersey_number = $_POST['jersey_number'];

    // Handle image update
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $targetDir = "uploads/";
        $targetFile = $targetDir. basename($_FILES["image"]["name"]);
        $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
        $uploadOk = 1;

        // Check if image file is a actual image or fake image
        $check = getimagesize($_FILES["image"]["tmp_name"]);
        if ($check === false) {
            echo "<div class='alert alert-danger'>File is not an image.</div>";
            $uploadOk = 0;
        }

        // Check file size (example: limit to 5MB)
        if ($_FILES["image"]["size"] > 5000000) {
            echo "<div class='alert alert-danger'>Sorry, your file is too large.</div>";
            $uploadOk = 0;
        }

        // Allow certain file formats
        if ($imageFileType!= "jpg" && $imageFileType!= "png" && $imageFileType!= "jpeg" && $imageFileType!= "gif") {
            echo "<div class='alert alert-danger'>Sorry, only JPG, JPEG, PNG & GIF files are allowed.</div>";
            $uploadOk = 0;
        }

        // Check if $uploadOk is set to 0 by an error
        if ($uploadOk == 0) {
            echo "<div class='alert alert-danger'>Sorry, your file was not uploaded.</div>";
        } else {
            if (move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {
                $imageName = basename($_FILES["image"]["name"]);

                // Delete the old image if it exists
                if (!empty($player['image']) && file_exists($targetDir. $player['image'])) {
                    unlink($targetDir. $player['image']);
                }

                // Update the image name in the database
                $sql = "UPDATE players SET image = '$imageName' WHERE id = $id";
                if (!$conn->query($sql)) {
                    echo "<div class='alert alert-danger'>Error updating image: ". $conn->error. "</div>";
                }
            } else {
                echo "<div class='alert alert-danger'>Sorry, there was an error uploading your file.</div>";
            }
        }
    }

    $sql = "UPDATE players SET name = '$name', team_id = '$team_id', position = '$position', jersey_number = '$jersey_number' WHERE id = $id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Player updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: ". $conn->error. "</div>";
    }
}?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Player</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
<div class="container mt-4">
    <h1>Edit Player</h1>
    <form method="POST" enctype="multipart/form-data">
        <div class="mb-3">
            <input type="text" name="name" placeholder="Player Name" class="form-control" value="<?php echo $player['name'];?>" required>
        </div>
        <div class="mb-3">
            <select name="team_id" class="form-control" required>
                <option value="">Select Team</option>
                <?php while ($row = $teams->fetch_assoc()) {?>
                <option value="<?php echo $row['id'];?>" <?php if ($row['id'] == $player['team_id']) echo 'selected';?>><?php echo $row['name'];?></option>
                <?php }?>
            </select>
        </div>
        <div class="mb-3">
            <input type="text" name="position" placeholder="Position" class="form-control" value="<?php echo $player['position'];?>" required>
        </div>
        <div class="mb-3">
            <input type="number" name="jersey_number" placeholder="Jersey Number" class="form-control" value="<?php echo $player['jersey_number'];?>" required>
        </div>
        <div class="mb-3">
            <label for="image" class="form-label">Player Image</label>
            <input type="file" name="image" id="image" class="form-control" accept="image/*">
            <?php if (!empty($player['image'])) {?>
                <img src="uploads/<?php echo $player['image'];?>" alt="<?php echo $player['name'];?> Image" width="50">
            <?php }?>
        </div>
        <button type="submit" name="update_player" class="btn btn-primary">Update Player</button>
    </form>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>