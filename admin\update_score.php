<?php
include '../includes/db.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);

    if (isset($data['match_id'], $data['home_team_score'], $data['away_team_score'], $data['status'])) {
        $match_id = $data['match_id'];
        $home_score = $data['home_team_score'];
        $away_score = $data['away_team_score'];
        $status = $data['status'];

        $sql = "UPDATE matches SET home_team_score = ?, away_team_score = ?, status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('dssi', $home_score, $away_score, $status, $match_id);

        if ($stmt->execute()) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Error updating match']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid data']);
    }
}
