<?php
require __DIR__ . '/../vendor/autoload.php'; // Include Composer's autoloader
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

use Twilio\Rest\Client;

$errors = [];
$success = "";

// Twilio credentials
$account_sid = '**********************************'; // Replace with your Twilio Account SID
$auth_token  = 'bff3a2c1023a095177010b0c6d0f0350';  // Replace with your Twilio Auth Token
$twilio_number = '****** 303 5561'; // Replace with your Twilio phone number

// --------------------
// Process OTP Generation and Sending
// --------------------
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_otp'])) {
    $phone = trim($_POST['phone']);

    // Validate phone number
    if (empty($phone)) {
        $errors[] = "Phone number is required.";
    } else {
        // Generate a 6-digit OTP
        $otp = rand(100000, 999999);
        $_SESSION['otp'] = $otp; // Store OTP in session
        $_SESSION['otp_phone'] = $phone; // Store phone number in session

        // Send OTP via Twilio
        try {
            $client = new Client($account_sid, $auth_token);
            $client->messages->create(
                $phone, // Recipient's phone number
                [
                    'from' => $twilio_number, // Your Twilio number
                    'body' => "Your OTP for admin creation is: $otp" // OTP message
                ]
            );
            $success = "OTP sent successfully to $phone.";
        } catch (Exception $e) {
            $errors[] = "Failed to send OTP: " . $e->getMessage();
        }
    }
}

// --------------------
// Process OTP Verification and Admin Creation
// --------------------
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_admin'])) {
    $new_username = trim($_POST['username']);
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);
    $otp_entered = trim($_POST['otp']);

    // Validate inputs
    if (empty($new_username)) {
        $errors[] = "Username is required.";
    }
    if (empty($password)) {
        $errors[] = "Password is required.";
    }
    if ($password !== $confirm_password) {
        $errors[] = "Passwords do not match.";
    }
    if (empty($otp_entered)) {
        $errors[] = "OTP is required.";
    }

    // Verify OTP
    if (empty($errors)) {
        if (!isset($_SESSION['otp']) || !isset($_SESSION['otp_phone'])) {
            $errors[] = "OTP session expired. Please request a new OTP.";
        } elseif ($otp_entered != $_SESSION['otp']) {
            $errors[] = "Invalid OTP. Please try again.";
        }
    }

    // Check for duplicate username
    if (empty($errors)) {
        $stmt = $conn->prepare("SELECT id FROM admins WHERE username = ?");
        $stmt->bind_param("s", $new_username);
        $stmt->execute();
        $stmt->store_result();
        if ($stmt->num_rows > 0) {
            $errors[] = "Username already exists.";
        }
        $stmt->close();
    }

    // If no errors, hash password and insert new admin
    if (empty($errors)) {
        $hashed_password = password_hash($password, PASSWORD_BCRYPT);
        $stmt = $conn->prepare("INSERT INTO admins (username, password) VALUES (?, ?)");
        $stmt->bind_param("ss", $new_username, $hashed_password);
        if ($stmt->execute()) {
            $success = "New admin created successfully!";
            // Clear OTP session after successful creation
            unset($_SESSION['otp']);
            unset($_SESSION['otp_phone']);
        } else {
            $errors[] = "Error: " . $conn->error;
        }
        $stmt->close();
    }
}

// Fetch all admin records for listing
$admins = $conn->query("SELECT * FROM admins");
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Manage Admins</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <style>
    body {
      background: #f8f9fa;
    }
    .card {
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .card-header {
      background: #343a40;
      color: #fff;
    }
    table td, table th {
      vertical-align: middle;
    }
  </style>
</head>
<body>
  <div class="container mt-5">
      <h1 class="mb-4 text-center">Admin Management</h1>
      <?php if (!empty($errors)): ?>
          <div class="alert alert-danger">
              <ul class="mb-0">
                  <?php foreach ($errors as $error): ?>
                      <li><?php echo htmlspecialchars($error); ?></li>
                  <?php endforeach; ?>
              </ul>
          </div>
      <?php endif; ?>

      <?php if (!empty($success)): ?>
          <div class="alert alert-success">
              <?php echo htmlspecialchars($success); ?>
          </div>
      <?php endif; ?>

      <!-- OTP Form -->
      <div class="card mb-4">
          <div class="card-header text-center">
              <h3>Send OTP</h3>
          </div>
          <div class="card-body">
              <form method="POST" action="">
                  <div class="mb-3">
                      <label for="phone" class="form-label">Phone Number</label>
                      <input type="tel" name="phone" id="phone" class="form-control" required 
                      placeholder="Enter phone number with country code (e.g., +1234567890)">
                  </div>
                  <div class="d-grid">
                      <button type="submit" name="send_otp" class="btn btn-primary">Send OTP</button>
                  </div>
              </form>
          </div>
      </div>

      <!-- Create Admin Form -->
      <div class="card mb-4">
          <div class="card-header text-center">
              <h3>Create New Admin</h3>
          </div>
          <div class="card-body">
              <form method="POST" action="">
                  <div class="mb-3">
                      <label for="username" class="form-label">Admin Username</label>
                      <input type="text" name="username" id="username" class="form-control" required 
                      value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                  </div>
                  <div class="mb-3">
                      <label for="password" class="form-label">Password</label>
                      <input type="password" name="password" id="password" class="form-control toggle-password" required>
                  </div>
                  <div class="mb-3">
                      <label for="confirm_password" class="form-label">Confirm Password</label>
                      <input type="password" name="confirm_password" id="confirm_password" class="form-control toggle-password" required>
                  </div>
                  <div class="mb-3">
                      <label for="otp" class="form-label">OTP</label>
                      <input type="text" name="otp" id="otp" class="form-control" required 
                      placeholder="Enter the OTP sent to your phone">
                  </div>
                  <div class="form-check mb-3">
                      <input type="checkbox" class="form-check-input" id="showPassword">
                      <label class="form-check-label" for="showPassword">Show Password</label>
                  </div>
                  <div class="d-grid">
                      <button type="submit" name="create_admin" class="btn btn-primary">Create Admin</button>
                  </div>
              </form>
          </div>
      </div>

      <!-- Admin List -->
      <div class="card">
          <div class="card-header text-center">
              <h3>All Admins</h3>
          </div>
          <div class="card-body">
              <table class="table table-bordered">
                  <thead>
                      <tr>
                          <th>ID</th>
                          <th>Username</th>
                          <th>Actions</th>
                      </tr>
                  </thead>
                  <tbody>
                      <?php while ($row = $admins->fetch_assoc()) : ?>
                          <tr>
                              <td><?php echo $row['id']; ?></td>
                              <td><?php echo htmlspecialchars($row['username']); ?></td>
                              <td>
                                  <a href="?action=edit&id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
                                  <?php if ($row['id'] != 5): ?>
                                      <a href="?action=delete&id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this admin?');">Delete</a>
                                  <?php else: ?>
                                      <button class="btn btn-secondary btn-sm" disabled>Delete</button>
                                  <?php endif; ?>
                              </td>
                          </tr>
                      <?php endwhile; ?>
                  </tbody>
              </table>
          </div>
      </div>
  </div>
  <!-- Toggle Password Visibility Script -->
  <script>
  document.addEventListener('DOMContentLoaded', function() {
      const showPassword = document.getElementById('showPassword');
      if(showPassword) {
          showPassword.addEventListener('change', function() {
              const toggleFields = document.querySelectorAll('#password, #confirm_password');
              toggleFields.forEach(function(field) {
                  field.type = showPassword.checked ? 'text' : 'password';
              });
          });
      }
  });
  </script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php include '../includes/footer.php'; ?>


