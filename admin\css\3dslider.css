/*------------------------------------------------------------------
    File Name: 3dslider.css
-------------------------------------------------------------------*/

#wrapper_bu {
  position: absolute;
  top: 150px;
  width: 100%;
  height: 100%;
  background-color: #ddd
}

.txt_Center { margin: 50% 0% 0% 20% }

.txt_h1 { font-size: 2em }

.holder_bu { cursor: pointer }

.holder_bu_awayL1 {
  position: absolute;
  top: 30%;
  left: 10%;
  width: 10%;
  height: 40%;
  background-image: url('images/img-01.jpg');
  transition: width 1s, height 1s, top 1s, left 1s
}

.holder_bu_awayL2 {
  position: absolute;
  top: 35%;
  left: 0%;
  width: 10%;
  height: 30%;
  background-image: url('images/img-02.jpg');
  transition: width 1s, height 1s, top 1s, left 1s
}

.holder_bu_center {
  position: absolute;
  top: 5%;
  left: 20%;
  width: 60%;
  height: 90%;
  background-image: url('images/img-03.jpg');
  transition: width 1s, height 1s, top 1s, left 1s
}

.holder_bu_awayR1 {
  position: absolute;
  top: 30%;
  left: 80%;
  width: 10%;
  height: 40%;
  background-image: url('images/img-04.jpg');
  transition: width 1s, height 1s, top 1s, left 1s
}

.holder_bu_awayR2 {
  position: absolute;
  top: 35%;
  left: 90%;
  width: 10%;
  height: 30%;
  background-color: rgba(0,0,50,0.5);
  -webkit-transition: width 1s, height 1s, top 1s, left 1s;
  -moz-transition: width 1s, height 1s, top 1s, left 1s;
  -o-transition: width 1s, height 1s, top 1s, left 1s;
  -ms-transition: width 1s, height 1s, top 1s, left 1s;
  transition: width 1s, height 1s, top 1s, left 1s
}
