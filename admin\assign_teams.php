<?php
include '../includes/db.php';
include 'check_login.php';
//include '../includes/header.php';
include './includes/header.php';

// Fetch all groups
$groups = $conn->query("SELECT * FROM `groups`");

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign_teams'])) {
    $group_id = $_POST['group_id'];
    $team_ids = isset($_POST['team_ids']) ? (array)$_POST['team_ids'] : []; // Ensure $team_ids is always an array

    // Start a transaction to ensure atomicity
    $conn->begin_transaction();
    try {
        foreach ($team_ids as $team_id) {
            // Check if the team is already in another group
            $check_sql = "SELECT group_id FROM group_teams WHERE team_id = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("i", $team_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                // Team is already in another group, throw an exception
                throw new Exception("Team with ID " . $team_id . " is already assigned to another group.");
            }

            // Insert the team into the selected group
            $insert_sql = "INSERT INTO group_teams (group_id, team_id) VALUES (?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("ii", $group_id, $team_id);
            $insert_stmt->execute();
        }

        // Commit the transaction if all insertions were successful
        $conn->commit();
        echo "<div class='alert alert-success'>Teams assigned to group successfully!</div>";
    } catch (Exception $e) {
        // Rollback the transaction if any insertion failed
        $conn->rollback();
        echo "<div class='alert alert-danger'>" . $e->getMessage() . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Teams to Group</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Assign Teams to Group</h1>
        <form method="POST">
            <div class="mb-3">
                <label class="form-label">Select Group</label>
                <select name="group_id" class="form-control" required>
                    <option value="">Select Group</option>
                    <?php while ($row = $groups->fetch_assoc()) { ?>
                        <option value="<?php echo $row['id']; ?>"><?php echo $row['name']; ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Select Teams</label>
                <div class="form-check" id="teams-container">
                    <?php
                    // Fetch all teams
                    $teams_sql = "SELECT * FROM `teams`";
                    $teams_result = $conn->query($teams_sql);

                    while ($team = $teams_result->fetch_assoc()) { ?>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="team_ids[]" value="<?php echo $team['id']; ?>" id="team_<?php echo $team['id']; ?>">
                            <label class="form-check-label" for="team_<?php echo $team['id']; ?>">
                                <?php echo $team['name']; ?>
                            </label>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <button type="submit" name="assign_teams" class="btn btn-primary">Assign Teams</button>
        </form>
    </div>
</body>
</html>