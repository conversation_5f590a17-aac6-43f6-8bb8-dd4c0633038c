<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// --- Goals Data ---
$goals_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_goals
                FROM players p
                INNER JOIN match_events me ON p.id = me.player_id
                INNER JOIN teams t ON p.team_id = t.id
                WHERE me.event_type = 'goal'
                GROUP BY p.id, p.name, t.name, p.image
                ORDER BY total_goals DESC";
$goals_result = $conn->query($goals_query);

// --- Assists Data ---
$assists_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_assists
                  FROM players p
                  INNER JOIN match_events me ON p.id = me.player_id
                  INNER JOIN teams t ON p.team_id = t.id
                  WHERE me.event_type = 'assist'
                  GROUP BY p.id, p.name, t.name, p.image
                  ORDER BY total_assists DESC";
$assists_result = $conn->query($assists_query);

// --- Clean Sheets Data ---
$clean_sheets_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_clean_sheets
                        FROM players p
                        INNER JOIN match_events me ON p.id = me.player_id
                        INNER JOIN teams t ON p.team_id = t.id
                        WHERE me.event_type = 'clean_sheet'
                        GROUP BY p.id, p.name, t.name, p.image
                        ORDER BY total_clean_sheets DESC";
$clean_sheets_result = $conn->query($clean_sheets_query);

// --- Yellow Cards Data ---
$yellow_cards_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_yellow_cards
                        FROM players p
                        INNER JOIN match_events me ON p.id = me.player_id
                        INNER JOIN teams t ON p.team_id = t.id
                        WHERE me.event_type = 'yellow_card'
                        GROUP BY p.id, p.name, t.name, p.image
                        ORDER BY total_yellow_cards DESC";
$yellow_cards_result = $conn->query($yellow_cards_query);

// --- Red Cards Data ---
$red_cards_query = "SELECT p.name, p.image, t.name as team_name, COUNT(me.player_id) AS total_red_cards
                    FROM players p
                    INNER JOIN match_events me ON p.id = me.player_id
                    INNER JOIN teams t ON p.team_id = t.id
                    WHERE me.event_type = 'red_card'
                    GROUP BY p.id, p.name, t.name, p.image
                    ORDER BY total_red_cards DESC";
$red_cards_result = $conn->query($red_cards_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>2024/25 Player Stats</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet"/>
  <!-- jsPDF and html2canvas for PDF generation -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    }
    .card {
      background-color: #ffffff;
      border-radius: 1rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0,0,0,0.15);
    }
    .card-header {
      border-top-left-radius: 1rem;
      border-top-right-radius: 1rem;
      padding: 1rem;
      color: #fff;
      font-weight: 700;
    }
    /* Custom gradients for each stat type */
    .goals-card .card-header { background: linear-gradient(90deg, #0d47a1, #1976d2); }
    .assists-card .card-header { background: linear-gradient(90deg, #616161, #9e9e9e); }
    .clean-sheets-card .card-header { background: linear-gradient(90deg, #00796b, #43a047); }
    .yellow-cards-card .card-header { background: linear-gradient(90deg, #f9a825, #fbc02d); }
    .red-cards-card .card-header { background: linear-gradient(90deg, #c62828, #e53935); }
    .top-player, .stat-item {
      padding: 0.75rem 1rem;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .top-player:last-child, .stat-item:last-child {
      border-bottom: none;
    }
    .top-player-details {
      display: flex;
      align-items: center;
    }
    .player-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .player-image {
      border-radius: 9999px;
      margin-right: 0.75rem;
      width: 50px;
      height: 50px;
      object-fit: cover;
      border: 2px solid #fff;
    }
    .stat-number {
      font-size: 1.5rem;
      font-weight: 700;
    }
    /* Button styles */
    .btn {
      display: inline-flex;
      align-items: center;
      padding: 0.5rem 1rem;
      border-radius: 9999px;
      font-weight: 600;
      transition: background-color 0.3s ease;
      cursor: pointer;
    }
    .btn-print {
      background-color: #4f46e5;
      color: #fff;
    }
    .btn-print:hover {
      background-color: #4338ca;
    }
    /* Hide extra rows initially */
    .extra-goals, .extra-assists, .extra-clean-sheets, .extra-yellow-cards, .extra-red-cards {
      display: none;
    }
  </style>
</head>
<body>
  <div class="container mx-auto p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-4xl font-bold text-gray-800">2024/25 Player Stats</h1>
      <button onclick="generatePDF()" class="btn btn-print">
        <i class="fas fa-file-pdf mr-2"></i>Download PDF
      </button>
    </div>
    <!-- PDF content container -->
    <div id="pdf-content">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      
        <!-- Goals Card -->
        <div class="goals-card card overflow-hidden">
          <div class="card-header">
            <i class="fas fa-futbol mr-2"></i>Goals
          </div>
          <?php if ($goals_result && $goals_result->num_rows > 0) : ?>
            <?php $rank = 1; ?>
            <?php $top_player = $goals_result->fetch_assoc(); ?>
            <div class="top-player">
              <div class="top-player-details">
                <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" />
                <div class="player-info">
                  <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                </div>
              </div>
              <div class="stat-number"><?php echo $top_player['total_goals']; ?></div>
            </div>
            <?php $counter = 0; ?>
            <?php while ($row = $goals_result->fetch_assoc()) : ?>
              <?php $counter++; ?>
              <div class="stat-item <?php echo ($counter > 4 ? 'extra-goals' : ''); ?>">
                <div class="player-info">
                  <h3><?php echo ($rank + $counter) . '. ' . htmlspecialchars($row['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                </div>
                <span class="stat-number"><?php echo $row['total_goals']; ?></span>
              </div>
            <?php endwhile; ?>
            <?php if ($counter > 4) : ?>
              <div class="p-4 text-center">
                <button class="btn bg-gray-200 text-gray-800" onclick="toggleExtra('extra-goals', this)">View Full List <i class="fas fa-arrow-right ml-2"></i></button>
              </div>
            <?php endif; ?>
          <?php else : ?>
            <p class="p-4">No goal data available.</p>
          <?php endif; ?>
          <?php $goals_result->data_seek(0); ?>
        </div>
      
        <!-- Assists Card -->
        <div class="assists-card card overflow-hidden">
          <div class="card-header">
            <i class="fas fa-hands-helping mr-2"></i>Assists
          </div>
          <?php if ($assists_result && $assists_result->num_rows > 0) : ?>
            <?php $rank = 1; ?>
            <?php $top_player = $assists_result->fetch_assoc(); ?>
            <div class="top-player">
              <div class="top-player-details">
                <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" />
                <div class="player-info">
                  <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                </div>
              </div>
              <div class="stat-number"><?php echo $top_player['total_assists']; ?></div>
            </div>
            <?php $counter = 0; ?>
            <?php while ($row = $assists_result->fetch_assoc()) : ?>
              <?php $counter++; ?>
              <div class="stat-item <?php echo ($counter > 4 ? 'extra-assists' : ''); ?>">
                <div class="player-info">
                  <h3><?php echo ($rank + $counter) . '. ' . htmlspecialchars($row['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                </div>
                <span class="stat-number"><?php echo $row['total_assists']; ?></span>
              </div>
            <?php endwhile; ?>
            <?php if ($counter > 4) : ?>
              <div class="p-4 text-center">
                <button class="btn bg-gray-200 text-gray-800" onclick="toggleExtra('extra-assists', this)">View Full List <i class="fas fa-arrow-right ml-2"></i></button>
              </div>
            <?php endif; ?>
          <?php else : ?>
            <p class="p-4">No assist data available.</p>
          <?php endif; ?>
          <?php $assists_result->data_seek(0); ?>
        </div>
      
        <!-- Clean Sheets Card -->
        <div class="clean-sheets-card card overflow-hidden">
          <div class="card-header">
            <i class="fas fa-shield-alt mr-2"></i>Clean Sheets
          </div>
          <?php if ($clean_sheets_result && $clean_sheets_result->num_rows > 0) : ?>
            <?php $rank = 1; ?>
            <?php $top_player = $clean_sheets_result->fetch_assoc(); ?>
            <div class="top-player">
              <div class="top-player-details">
                <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" />
                <div class="player-info">
                  <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                </div>
              </div>
              <div class="stat-number"><?php echo $top_player['total_clean_sheets']; ?></div>
            </div>
            <?php $counter = 0; ?>
            <?php while ($row = $clean_sheets_result->fetch_assoc()) : ?>
              <?php $counter++; ?>
              <div class="stat-item <?php echo ($counter > 4 ? 'extra-clean-sheets' : ''); ?>">
                <div class="player-info">
                  <h3><?php echo ($rank + $counter) . '. ' . htmlspecialchars($row['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                </div>
                <span class="stat-number"><?php echo $row['total_clean_sheets']; ?></span>
              </div>
            <?php endwhile; ?>
            <?php if ($counter > 4) : ?>
              <div class="p-4 text-center">
                <button class="btn bg-gray-200 text-gray-800" onclick="toggleExtra('extra-clean-sheets', this)">View Full List <i class="fas fa-arrow-right ml-2"></i></button>
              </div>
            <?php endif; ?>
          <?php else : ?>
            <p class="p-4">No clean sheet data available.</p>
          <?php endif; ?>
          <?php $clean_sheets_result->data_seek(0); ?>
        </div>
      
        <!-- Yellow Cards Card -->
        <div class="yellow-cards-card card overflow-hidden">
          <div class="card-header">
            <i class="fas fa-ticket-alt mr-2"></i>Yellow Cards
          </div>
          <?php if ($yellow_cards_result && $yellow_cards_result->num_rows > 0) : ?>
            <?php $rank = 1; ?>
            <?php $top_player = $yellow_cards_result->fetch_assoc(); ?>
            <div class="top-player">
              <div class="top-player-details">
                <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" />
                <div class="player-info">
                  <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                </div>
              </div>
              <div class="stat-number"><?php echo $top_player['total_yellow_cards']; ?></div>
            </div>
            <?php $counter = 0; ?>
            <?php while ($row = $yellow_cards_result->fetch_assoc()) : ?>
              <?php $counter++; ?>
              <div class="stat-item <?php echo ($counter > 4 ? 'extra-yellow-cards' : ''); ?>">
                <div class="player-info">
                  <h3><?php echo ($rank + $counter) . '. ' . htmlspecialchars($row['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                </div>
                <span class="stat-number"><?php echo $row['total_yellow_cards']; ?></span>
              </div>
            <?php endwhile; ?>
            <?php if ($counter > 4) : ?>
              <div class="p-4 text-center">
                <button class="btn bg-gray-200 text-gray-800" onclick="toggleExtra('extra-yellow-cards', this)">View Full List <i class="fas fa-arrow-right ml-2"></i></button>
              </div>
            <?php endif; ?>
          <?php else : ?>
            <p class="p-4">No yellow card data available.</p>
          <?php endif; ?>
          <?php $yellow_cards_result->data_seek(0); ?>
        </div>
      
        <!-- Red Cards Card -->
        <div class="red-cards-card card overflow-hidden">
          <div class="card-header">
            <i class="fas fa-ban mr-2"></i>Red Cards
          </div>
          <?php if ($red_cards_result && $red_cards_result->num_rows > 0) : ?>
            <?php $rank = 1; ?>
            <?php $top_player = $red_cards_result->fetch_assoc(); ?>
            <div class="top-player">
              <div class="top-player-details">
                <img alt="<?php echo htmlspecialchars($top_player['name']); ?>" class="player-image" src="uploads/<?php echo htmlspecialchars($top_player['image']); ?>" />
                <div class="player-info">
                  <h3 class="text-lg font-bold"><?php echo htmlspecialchars($top_player['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($top_player['team_name']); ?></p>
                </div>
              </div>
              <div class="stat-number"><?php echo $top_player['total_red_cards']; ?></div>
            </div>
            <?php $counter = 0; ?>
            <?php while ($row = $red_cards_result->fetch_assoc()) : ?>
              <?php $counter++; ?>
              <div class="stat-item <?php echo ($counter > 4 ? 'extra-red-cards' : ''); ?>">
                <div class="player-info">
                  <h3><?php echo ($rank + $counter) . '. ' . htmlspecialchars($row['name']); ?></h3>
                  <p class="text-sm"><?php echo htmlspecialchars($row['team_name']); ?></p>
                </div>
                <span class="stat-number"><?php echo $row['total_red_cards']; ?></span>
              </div>
            <?php endwhile; ?>
            <?php if ($counter > 4) : ?>
              <div class="p-4 text-center">
                <button class="btn bg-gray-200 text-gray-800" onclick="toggleExtra('extra-red-cards', this)">View Full List <i class="fas fa-arrow-right ml-2"></i></button>
              </div>
            <?php endif; ?>
          <?php else : ?>
            <p class="p-4">No red card data available.</p>
          <?php endif; ?>
          <?php $red_cards_result->data_seek(0); ?>
        </div>
      
      </div>
    </div>
  </div>

  <!-- PDF Generation Script -->
  <script>
    function generatePDF() {
      const { jsPDF } = window.jspdf;
      const element = document.getElementById('pdf-content');
      html2canvas(element, { scale: 2 }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'pt', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        // Calculate image height to maintain aspect ratio
        const imgProps = pdf.getImageProperties(imgData);
        const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;
        pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, imgHeight);
        pdf.save("player_stats.pdf");
      });
    }
    
    function toggleExtra(className, btn) {
      var elems = document.querySelectorAll('.' + className);
      elems.forEach(function(el) {
        if (el.style.display === 'none' || el.style.display === '') {
          el.style.display = 'flex';
        } else {
          el.style.display = 'none';
        }
      });
      if (btn.innerText.trim().startsWith('View Full List')) {
        btn.innerHTML = 'View Less <i class="fas fa-arrow-up ml-2"></i>';
      } else {
        btn.innerHTML = 'View Full List <i class="fas fa-arrow-right ml-2"></i>';
      }
    }
  </script>
</body>
</html>
<?php 
$conn->close();
include '../includes/footer.php'; 
?>
