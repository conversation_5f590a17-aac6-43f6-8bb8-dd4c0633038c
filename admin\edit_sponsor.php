<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

if (!isset($_GET['id'])) {
    header("Location: sponsors.php");
    exit();
}

$id = $_GET['id'];

// Fetch sponsor details
$sponsor = $conn->query("SELECT * FROM sponsors WHERE id = $id")->fetch_assoc();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $logo = $_FILES['logo']['name'];
    $pictures = $_FILES['pictures'];
    $business_type = $_POST['business_type'];
    $contact_email = $_POST['contact_email'];
    $contact_phone = $_POST['contact_phone'];
    $about_us = $_POST['about_us'];
    $our_services = $_POST['our_services'];
    $mission = $_POST['mission'];
    $details = $_POST['details'];

    // Validate that at least 5 pictures are uploaded
    if (count($pictures['name']) < 5) {
        echo "<div class='alert alert-danger'>You must upload at least 5 pictures.</div>";
    } else {
        // Upload logo if a new one is provided
        if (!empty($logo)) {
            $logo_path = "../assets/uploads/logos/" . basename($logo);
            move_uploaded_file($_FILES['logo']['tmp_name'], $logo_path);
        } else {
            $logo_path = $sponsor['logo'];
        }

        // Upload pictures if new ones are provided
        if (!empty($pictures['name'][0])) {
            $picture_paths = [];
            foreach ($pictures['tmp_name'] as $key => $tmp_name) {
                $picture_name = $pictures['name'][$key];
                $picture_path = "../assets/uploads/pictures/" . basename($picture_name);
                move_uploaded_file($tmp_name, $picture_path);
                $picture_paths[] = $picture_path;
            }
            $pictures_json = json_encode($picture_paths);
        } else {
            $pictures_json = $sponsor['pictures'];
        }

        // Update sponsor in the database
        $sql = "UPDATE sponsors SET 
                name = '$name', 
                logo = '$logo_path', 
                pictures = '$pictures_json', 
                business_type = '$business_type', 
                contact_email = '$contact_email', 
                contact_phone = '$contact_phone', 
                about_us = '$about_us', 
                our_services = '$our_services', 
                mission = '$mission', 
                details = '$details' 
                WHERE id = $id";
        if ($conn->query($sql)) {
            echo "<div class='alert alert-success'>Sponsor updated successfully!</div>";
        } else {
            echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Sponsor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Edit Sponsor</h1>
        <a href="sponsors.php" class="btn btn-secondary mb-3">Back to Sponsors</a>
        <form method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label class="form-label">Name</label>
                <input type="text" name="name" class="form-control" value="<?php echo $sponsor['name']; ?>" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Logo</label>
                <input type="file" name="logo" class="form-control">
                <img src="<?php echo $sponsor['logo']; ?>" alt="Logo" width="50">
            </div>
            <div class="mb-3">
                <label class="form-label">Pictures (At least 5)</label>
                <input type="file" name="pictures[]" class="form-control" multiple>
                <?php
                $pictures = json_decode($sponsor['pictures'], true);
                foreach ($pictures as $picture) {
                    echo "<img src='$picture' alt='Picture' width='50' class='me-2'>";
                }
                ?>
            </div>
            <div class="mb-3">
                <label class="form-label">Business Type</label>
                <input type="text" name="business_type" class="form-control" value="<?php echo $sponsor['business_type']; ?>" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Contact Email</label>
                <input type="email" name="contact_email" class="form-control" value="<?php echo $sponsor['contact_email']; ?>" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Contact Phone</label>
                <input type="text" name="contact_phone" class="form-control" value="<?php echo $sponsor['contact_phone']; ?>" required>
            </div>
            <div class="mb-3">
                <label class="form-label">About Us</label>
                <textarea name="about_us" class="form-control" rows="4" required><?php echo $sponsor['about_us']; ?></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Our Services</label>
                <textarea name="our_services" class="form-control" rows="4" required><?php echo $sponsor['our_services']; ?></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Mission</label>
                <textarea name="mission" class="form-control" rows="4" required><?php echo $sponsor['mission']; ?></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Details</label>
                <textarea name="details" class="form-control" rows="4" required><?php echo $sponsor['details']; ?></textarea>
            </div>
            <button type="submit" class="btn btn-primary">Update Sponsor</button>
        </form>
    </div>
    
</body>
</html>
<?php include '../includes/footer.php'; ?>