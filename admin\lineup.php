<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Check if a specific match is passed via GET
$selected_match_id = isset($_GET['match_id']) ? $_GET['match_id'] : '';

// Fetch Matches
$matches = $conn->query("SELECT * FROM matches");

// Fetch Teams
$teams = $conn->query("SELECT * FROM teams");
$team_list = [];
while ($row = $teams->fetch_assoc()) {
    $team_list[$row['id']] = $row['name'];
}

// If a match is selected, fetch its details
$selected_match_info = null;
if ($selected_match_id) {
    $match_query = $conn->query("SELECT * FROM matches WHERE id = '$selected_match_id'");
    if ($match_query && $match_query->num_rows > 0) {
        $selected_match_info = $match_query->fetch_assoc();
    }
}

// Create or Update Match Details
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_details'])) {
    $match_id = $_POST['match_id'];
    $home_coach = $_POST['home_coach'];
    $away_coach = $_POST['away_coach'];

    // Process Home Starting
    $home_starting_string = $_POST['home_starting'];
    $home_starting_array = explode("\n", $home_starting_string);
    $home_starting_array = array_map('trim', $home_starting_array);
    $home_starting_array = array_filter($home_starting_array);
    $home_starting = implode(',', $home_starting_array);

    // Process Away Starting
    $away_starting_string = $_POST['away_starting'];
    $away_starting_array = explode("\n", $away_starting_string);
    $away_starting_array = array_map('trim', $away_starting_array);
    $away_starting_array = array_filter($away_starting_array);
    $away_starting = implode(',', $away_starting_array);

    // Process Home Subs
    $home_subs_string = $_POST['home_subs'];
    $home_subs_array = explode("\n", $home_subs_string);
    $home_subs_array = array_map('trim', $home_subs_array);
    $home_subs_array = array_filter($home_subs_array);
    $home_subs = implode(',', $home_subs_array);

    // Process Away Subs
    $away_subs_string = $_POST['away_subs'];
    $away_subs_array = explode("\n", $away_subs_string);
    $away_subs_array = array_map('trim', $away_subs_array);
    $away_subs_array = array_filter($away_subs_array);
    $away_subs = implode(',', $away_subs_array);

    $sql = "REPLACE INTO match_details (match_id, home_coach, away_coach, home_starting, away_starting, home_subs, away_subs) 
            VALUES ('$match_id', '$home_coach', '$away_coach', '$home_starting', '$away_starting', '$home_subs', '$away_subs')";

    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Match details saved successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}

// Fetch Match Details (for previously saved data)
$match_details = $conn->query("SELECT * FROM match_details");
$details = [];
while ($row = $match_details->fetch_assoc()) {
    $details[$row['match_id']] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Details Management</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
<div class="container mt-4">
    <h1 class="text-center">Manage Match Details</h1>
    <form method="POST">
        <div class="mb-3">
            <?php if ($selected_match_id && $selected_match_info): ?>
                <!-- If a match is provided via GET, display its details and use a hidden input -->
                <label class="form-label">Match:</label>
                <div>
                    <?php 
                        echo $team_list[$selected_match_info['home_team_id']] . " vs " . $team_list[$selected_match_info['away_team_id']]; 
                    ?>
                </div>
                <input type="hidden" name="match_id" value="<?php echo $selected_match_id; ?>">
            <?php else: ?>
                <label class="form-label">Select Match</label>
                <select name="match_id" class="form-control" required>
                    <option value="">Select a Match</option>
                    <?php while ($row = $matches->fetch_assoc()) { ?>
                        <option value="<?php echo $row['id']; ?>">
                            <?php echo $team_list[$row['home_team_id']] . " vs " . $team_list[$row['away_team_id']]; ?>
                        </option>
                    <?php } ?>
                </select>
            <?php endif; ?>
        </div>
        <div class="mb-3">
            <label class="form-label">Home Coach</label>
            <input type="text" name="home_coach" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Away Coach</label>
            <input type="text" name="away_coach" class="form-control" required>
        </div>

        <div class="mb-3">
            <label class="form-label">Home Starting Eleven (Name, Position)</label><br>
            <textarea name="home_starting" class="form-control" rows="5" placeholder="Enter each player on a new line: Name,Position"></textarea>
        </div>

        <div class="mb-3">
            <label class="form-label">Away Starting Eleven (Name, Position)</label><br>
            <textarea name="away_starting" class="form-control" rows="5" placeholder="Enter each player on a new line: Name,Position"></textarea>
        </div>

        <div class="mb-3">
            <label class="form-label">Home Substitutes (Name, Position)</label><br>
            <textarea name="home_subs" class="form-control" rows="5" placeholder="Enter each player on a new line: Name,Position"></textarea>
        </div>

        <div class="mb-3">
            <label class="form-label">Away Substitutes (Name, Position)</label><br>
            <textarea name="away_subs" class="form-control" rows="5" placeholder="Enter each player on a new line: Name,Position"></textarea>
        </div>

        <button type="submit" name="save_details" class="btn btn-primary w-100">Save Match Details</button>
    </form>
</div>
</body>
</html>
