<?php
class Auth {
    private $db;
    private static $instance = null;
    
    private function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function login($username, $password, $isAdmin = false) {
        $table = $isAdmin ? 'administrators' : 'users';
        $query = "SELECT * FROM {$table} WHERE username = :username";
        
        try {
            $stmt = $this->db->prepare($query);
            $stmt->execute(['username' => $username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                $this->startSession($user, $isAdmin);
                $this->updateLastLogin($user['id'], $table);
                return true;
            }
            return false;
        } catch (PDOException $e) {
            $this->logError("Login error: " . $e->getMessage());
            return false;
        }
    }
    
    private function startSession($user, $isAdmin) {
        session_start();
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['is_admin'] = $isAdmin;
        $_SESSION['last_activity'] = time();
    }
    
    private function updateLastLogin($userId, $table) {
        $query = "UPDATE {$table} SET last_login = NOW() WHERE id = :id";
        try {
            $stmt = $this->db->prepare($query);
            $stmt->execute(['id' => $userId]);
        } catch (PDOException $e) {
            $this->logError("Error updating last login: " . $e->getMessage());
        }
    }
    
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && 
               (time() - $_SESSION['last_activity']) < SESSION_LIFETIME;
    }
    
    public function isAdmin() {
        return $this->isLoggedIn() && isset($_SESSION['is_admin']) && $_SESSION['is_admin'];
    }
    
    public function logout() {
        session_destroy();
        return true;
    }
}
