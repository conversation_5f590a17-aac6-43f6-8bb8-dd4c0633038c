<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';
// Create Player
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_player'])) {
    $name = $_POST['name'];
    $team_id = $_POST['team_id'];
    $position = $_POST['position'];
    $jersey_number = $_POST['jersey_number'];

    // Handle image upload
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $targetDir = "uploads/"; // Directory to store uploaded images
        $targetFile = $targetDir. basename($_FILES["image"]["name"]);
        $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
        $uploadOk = 1;

        // Check if image file is a actual image or fake image
        $check = getimagesize($_FILES["image"]["tmp_name"]);
        if ($check === false) {
            echo "<div class='alert alert-danger'>File is not an image.</div>";
            $uploadOk = 0;
        }

        // Check file size (example: limit to 5MB)
        if ($_FILES["image"]["size"] > 5000000) {
            echo "<div class='alert alert-danger'>Sorry, your file is too large.</div>";
            $uploadOk = 0;
        }

        // Allow certain file formats
        if ($imageFileType!= "jpg" && $imageFileType!= "png" && $imageFileType!= "jpeg" && $imageFileType!= "gif") {
            echo "<div class='alert alert-danger'>Sorry, only JPG, JPEG, PNG & GIF files are allowed.</div>";
            $uploadOk = 0;
        }

        // Check if $uploadOk is set to 0 by an error
        if ($uploadOk == 0) {
            echo "<div class='alert alert-danger'>Sorry, your file was not uploaded.</div>";
        } else {
            // If everything is ok, try to upload file
            if (move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {
                $imageName = basename($_FILES["image"]["name"]); // Get the image file name
            } else {
                echo "<div class='alert alert-danger'>Sorry, there was an error uploading your file.</div>";
            }
        }
    } else {
        $imageName = ""; // No image uploaded
    }

    $sql = "INSERT INTO players (name, team_id, position, jersey_number, image) 
            VALUES ('$name', '$team_id', '$position', '$jersey_number', '$imageName')";

    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Player created successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: ". $conn->error. "</div>";
    }
}

// Fetch Players
$players = $conn->query("SELECT players.*, teams.name as team_name FROM players JOIN teams ON players.team_id = teams.id");
$teams = $conn->query("SELECT * FROM teams");?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Players</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
<div class="container mt-4">
    <h1>Manage Players</h1>

    <form method="POST" class="mb-4" enctype="multipart/form-data">
        <div class="mb-3">
            <input type="text" name="name" placeholder="Player Name" class="form-control" required>
        </div>
        <div class="mb-3">
            <select name="team_id" class="form-control" required>
                <option value="">Select Team</option>
                <?php while ($row = $teams->fetch_assoc()) {?>
                <option value="<?php echo $row['id'];?>"><?php echo $row['name'];?></option>
                <?php }?>
            </select>
        </div>
        <div class="mb-3">
            <input type="text" name="position" placeholder="Position" class="form-control" required>
        </div>
        <div class="mb-3">
            <input type="number" name="jersey_number" placeholder="Jersey Number" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="image" class="form-label">Player Image</label>
            <input type="file" name="image" id="image" class="form-control" accept="image/*">
        </div>
        <button type="submit" name="create_player" class="btn btn-primary">Create Player</button>
    </form>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Team</th>
                <th>Position</th>
                <th>Jersey Number</th>
                <th>Image</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $players->fetch_assoc()) {?>
            <tr>
                <td><?php echo $row['id'];?></td>
                <td><?php echo $row['name'];?></td>
                <td><?php echo $row['team_name'];?></td>
                <td><?php echo $row['position'];?></td>
                <td><?php echo $row['jersey_number'];?></td>
                <td>
                    <?php if (!empty($row['image'])) {?>
                        <img src="uploads/<?php echo $row['image'];?>" alt="<?php echo $row['name'];?> Image" width="50">
                    <?php } else {?>
                        No Image
                    <?php }?>
                </td>
                <td>
                    <a href="edit_player.php?id=<?php echo $row['id'];?>" class="btn btn-warning btn-sm">Edit</a>
                    <a href="delete_player.php?id=<?php echo $row['id'];?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</a>
                </td>
            </tr>
            <?php }?>
        </tbody>
    </table>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php include '../includes/footer.php'; ?>