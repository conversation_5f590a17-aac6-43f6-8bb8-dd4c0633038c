<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';

// Function to update or insert standings
function updateStandings($conn, $group_id, $team_id, $played, $won, $drawn, $lost, $goals_for, $goals_against, $points) {
    $check_query = "SELECT id FROM standings WHERE group_id = ? AND team_id = ?";
    $check_stmt = $conn->prepare($check_query);
    if (!$check_stmt) {
        die("Prepare failed (checkQuery): " . $conn->error);
    }
    $check_stmt->bind_param("ii", $group_id, $team_id);
    $check_stmt->execute();
    $check_stmt->store_result();

    if ($check_stmt->num_rows > 0) {
        $update_query = "UPDATE standings SET played = ?, won = ?, drawn = ?, lost = ?, goals_for = ?, goals_against = ?, points = ? WHERE group_id = ? AND team_id = ?";
        $update_stmt = $conn->prepare($update_query);
        if (!$update_stmt) {
            die("Prepare failed (updateQuery): " . $conn->error);
        }
        $update_stmt->bind_param("iiiiiiiii", $played, $won, $drawn, $lost, $goals_for, $goals_against, $points, $group_id, $team_id);
        $update_stmt->execute();
        $update_stmt->close();
    } else {
        $insert_query = "INSERT INTO standings (group_id, team_id, played, won, drawn, lost, goals_for, goals_against, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        if (!$insert_stmt) {
            die("Prepare failed (insertQuery): " . $conn->error);
        }
        $insert_stmt->bind_param("iiiiiiiii", $group_id, $team_id, $played, $won, $drawn, $lost, $goals_for, $goals_against, $points);
        $insert_stmt->execute();
        $insert_stmt->close();
    }
    $check_stmt->close();
}

// Fetch all groups
$groups_query = "SELECT * FROM `groups`";
$groups_result = $conn->query($groups_query);

if ($groups_result && $groups_result->num_rows > 0) {
    while ($group = $groups_result->fetch_assoc()) {
        $group_id = $group['id'];

        // Fetch teams for this group using the group_teams table (wrapped in backticks)
        $teams_query = "SELECT team_id FROM `group_teams` WHERE group_id = ?";
        $teams_stmt = $conn->prepare($teams_query);
        if (!$teams_stmt) {
            die("Prepare failed (teamsQuery): " . $conn->error);
        }
        $teams_stmt->bind_param("i", $group_id);
        $teams_stmt->execute();
        $teams_result = $teams_stmt->get_result();

        if ($teams_result && $teams_result->num_rows > 0) {
            while ($team_row = $teams_result->fetch_assoc()) {
                $team_id = $team_row['team_id'];

                // Initialize stats
                $played = 0;
                $won = 0;
                $drawn = 0;
                $lost = 0;
                $goals_for = 0;
                $goals_against = 0;
                $points = 0;

                // Fetch only group stage matches for this team in this group.
                // This query joins matches and fixtures and filters by f.round = 'Group Stage'
                $matches_query = "SELECT m.* FROM matches m
                                  JOIN fixtures f ON m.id = f.match_id
                                  WHERE (m.home_team_id = ? OR m.away_team_id = ?)
                                  AND m.status = 'finished'
                                  AND f.round = 'Group Stage'";
                $matches_stmt = $conn->prepare($matches_query);
                if (!$matches_stmt) {
                    die("Prepare failed (matchesQuery): " . $conn->error);
                }
                // There are only two placeholders, so we bind two parameters.
                $matches_stmt->bind_param("ii", $team_id, $team_id);
                $matches_stmt->execute();
                $matches_result = $matches_stmt->get_result();

                if ($matches_result && $matches_result->num_rows > 0) {
                    while ($match = $matches_result->fetch_assoc()) {
                        $played++;

                        if ($match['home_team_id'] == $team_id) {
                            $goals_for += $match['home_team_score'];
                            $goals_against += $match['away_team_score'];

                            if ($match['home_team_score'] > $match['away_team_score']) {
                                $won++;
                                $points += 3;
                            } elseif ($match['home_team_score'] == $match['away_team_score']) {
                                $drawn++;
                                $points += 1;
                            } else {
                                $lost++;
                            }
                        } else { // Away team
                            $goals_for += $match['away_team_score'];
                            $goals_against += $match['home_team_score'];

                            if ($match['away_team_score'] > $match['home_team_score']) {
                                $won++;
                                $points += 3;
                            } elseif ($match['away_team_score'] == $match['home_team_score']) {
                                $drawn++;
                                $points += 1;
                            } else {
                                $lost++;
                            }
                        }
                    }
                }
                $matches_stmt->close();

                updateStandings($conn, $group_id, $team_id, $played, $won, $drawn, $lost, $goals_for, $goals_against, $points);
            }
        }
        $teams_stmt->close();
    }
}

// Fetch all groups and their standings (for display) – after standings update
$groups_query = "SELECT * FROM `groups`";
$groups_result = $conn->query($groups_query);

$all_standings = array();

if ($groups_result && $groups_result->num_rows > 0) {
    while ($group = $groups_result->fetch_assoc()) {
        $group_id = $group['id'];
        $group_name = $group['name'];

        $standings_query = "SELECT standings.*, teams.name as team_name, teams.logo as team_logo
                            FROM standings
                            JOIN teams ON standings.team_id = teams.id
                            WHERE standings.group_id = ?
                            ORDER BY standings.points DESC, (standings.goals_for - standings.goals_against) DESC, standings.goals_for DESC";
        $stmt = $conn->prepare($standings_query);
        if (!$stmt) {
            die("Prepare failed (standingsQuery): " . $conn->error);
        }
        $stmt->bind_param("i", $group_id);
        $stmt->execute();
        $standings_result = $stmt->get_result();
        $standings = array();
        if ($standings_result && $standings_result->num_rows > 0) {
            while ($row = $standings_result->fetch_assoc()) {
                $standings[] = $row;
            }
        }
        $all_standings[$group_name] = $standings;
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standings</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" 
          integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" 
          crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        .group-card {
            margin-bottom: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .team-logo {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            vertical-align: middle;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Standings</h1>
        <?php if (!empty($all_standings)): ?>
            <div class="row">
                <?php foreach ($all_standings as $group_name => $standings): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="card group-card">
                            <div class="card-header bg-primary text-white">
                                <h4><?php echo htmlspecialchars($group_name); ?></h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Team</th>
                                                <th>P</th>
                                                <th>W</th>
                                                <th>D</th>
                                                <th>L</th>
                                                <th>GF</th>
                                                <th>GA</th>
                                                <th>GD</th>
                                                <th>Pts</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($standings)): ?>
                                                <?php foreach ($standings as $row): ?>
                                                    <tr>
                                                        <td>
                                                            <?php if (!empty($row['team_logo'])): ?>
                                                                <img src="uploads/<?php echo htmlspecialchars($row['team_logo']); ?>" 
                                                                     alt="<?php echo htmlspecialchars($row['team_name']); ?> Logo" class="team-logo">
                                                            <?php endif; ?>
                                                            <?php echo htmlspecialchars($row['team_name']); ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($row['played']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['won']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['drawn']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['lost']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['goals_for']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['goals_against']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['goals_for'] - $row['goals_against']); ?></td>
                                                        <td><?php echo htmlspecialchars($row['points']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr><td colspan="9">No standings data available for this group.</td></tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p>No standings data available.</p>
        <?php endif; ?>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto refresh the page every 60 seconds
        setTimeout(function() {
            location.reload(1);
        }, 60000);
    </script>
</body>
</html>

<?php
$conn->close();
?>
