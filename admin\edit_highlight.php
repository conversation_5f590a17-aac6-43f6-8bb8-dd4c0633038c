<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

$id = $_GET['id'];

// Fetch highlight details with match and team information
$highlight = $conn->query("SELECT match_highlights.*, t1.name as home_team, t2.name as away_team, matches.date 
                           FROM match_highlights 
                           JOIN matches ON match_highlights.match_id = matches.id 
                           JOIN teams t1 ON matches.home_team_id = t1.id 
                           JOIN teams t2 ON matches.away_team_id = t2.id 
                           WHERE match_highlights.id = $id")->fetch_assoc();

// Handle form submission for editing highlights
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $video_title = $_POST['video_title'];
    $video_description = $_POST['video_description'];
    $video_link = $_POST['video_link'];
    $thumbnail = $_FILES['thumbnail']['name'];

    // Update thumbnail if a new one is uploaded
    if (!empty($thumbnail)) {
        $thumbnail_path = "../assets/uploads/thumbnails/" . basename($thumbnail);
        move_uploaded_file($_FILES['thumbnail']['tmp_name'], $thumbnail_path);
    } else {
        $thumbnail_path = $highlight['thumbnail'];
    }

    // Update database
    $sql = "UPDATE match_highlights 
            SET video_title = '$video_title', video_description = '$video_description', video_link = '$video_link', thumbnail = '$thumbnail_path' 
            WHERE id = $id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Highlight updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Highlight</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-container {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .thumbnail-preview {
            max-width: 100px;
            max-height: 100px;
            margin-top: 10px;
        }
        .btn-return {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Edit Highlight</h1>
        <div class="form-container">
            <!-- Display Match Information (Non-Editable) -->
            <div class="mb-3">
                <label class="form-label">Match</label>
                <input type="text" class="form-control" value="<?php echo $highlight['home_team'] . " vs " . $highlight['away_team'] . " (" . date('d M Y H:i', strtotime($highlight['date'])) . ")"; ?>" readonly>
            </div>

            <!-- Editable Fields -->
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label class="form-label">Video Title</label>
                    <input type="text" name="video_title" class="form-control" value="<?php echo $highlight['video_title']; ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Video Description</label>
                    <textarea name="video_description" class="form-control" rows="3" required><?php echo $highlight['video_description']; ?></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Video Link (e.g., YouTube)</label>
                    <input type="url" name="video_link" class="form-control" value="<?php echo $highlight['video_link']; ?>" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">Thumbnail</label>
                    <input type="file" name="thumbnail" class="form-control">
                    <small class="text-muted">Leave blank to keep the current thumbnail.</small>
                    <?php if (!empty($highlight['thumbnail'])) { ?>
                        <div class="mt-2">
                            <img src="<?php echo $highlight['thumbnail']; ?>" alt="Current Thumbnail" class="thumbnail-preview">
                        </div>
                    <?php } ?>
                </div>
                <button type="submit" class="btn btn-primary">Update Highlight</button>
            </form>

            <!-- Return to Manage Highlights Button -->
            <div class="btn-return">
                <a href="manage_highlights.php" class="btn btn-secondary">Return to Manage Highlights</a>
            </div>
        </div>
    </div>
</body>
</html>

<?php include '../includes/footer.php'; ?>