<?php
include '../includes/db.php';
include './includes/header.php';
include 'check_login.php';

// Handle form submission for adding/editing sponsors
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $logo = $_FILES['logo']['name'];
    $pictures = $_FILES['pictures'];
    $business_type = $_POST['business_type'];
    $contact_email = $_POST['contact_email'];
    $contact_phone = $_POST['contact_phone'];
    $about_us = $_POST['about_us'];
    $our_services = $_POST['our_services'];
    $mission = $_POST['mission'];
    $details = $_POST['details'];

    // Validate that at least 5 pictures are uploaded
    if (count($pictures['name']) < 5) {
        echo "<div class='alert alert-danger'>You must upload at least 5 pictures.</div>";
    } else {
        // Upload logo
        $logo_path = "../assets/uploads/logos/" . basename($logo);
        move_uploaded_file($_FILES['logo']['tmp_name'], $logo_path);

        // Upload pictures
        $picture_paths = [];
        foreach ($pictures['tmp_name'] as $key => $tmp_name) {
            $picture_name = $pictures['name'][$key];
            $picture_path = "../assets/uploads/pictures/" . basename($picture_name);
            move_uploaded_file($tmp_name, $picture_path);
            $picture_paths[] = $picture_path;
        }
        $pictures_json = json_encode($picture_paths);

        // Insert into database
        $sql = "INSERT INTO sponsors (name, logo, pictures, business_type, contact_email, contact_phone, about_us, our_services, mission, details) 
                VALUES ('$name', '$logo_path', '$pictures_json', '$business_type', '$contact_email', '$contact_phone', '$about_us', '$our_services', '$mission', '$details')";
        if ($conn->query($sql)) {
            echo "<div class='alert alert-success'>Sponsor added successfully!</div>";
        } else {
            echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
        }
    }
}

// Fetch all sponsors
$sponsors = $conn->query("SELECT * FROM sponsors");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Sponsors</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Manage Sponsors</h1>

        <!-- Add Sponsor Form -->
        <form method="POST" enctype="multipart/form-data" class="mb-4">
            <div class="mb-3">
                <label class="form-label">Name</label>
                <input type="text" name="name" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Logo</label>
                <input type="file" name="logo" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Pictures (At least 5)</label>
                <input type="file" name="pictures[]" class="form-control" multiple required>
                <small class="text-muted">You must upload at least 5 pictures.</small>
            </div>
            <div class="mb-3">
                <label class="form-label">Business Type</label>
                <input type="text" name="business_type" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Contact Email</label>
                <input type="email" name="contact_email" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Contact Phone</label>
                <input type="text" name="contact_phone" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">About Us</label>
                <textarea name="about_us" class="form-control" rows="4" required></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Our Services</label>
                <textarea name="our_services" class="form-control" rows="4" required></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Mission</label>
                <textarea name="mission" class="form-control" rows="4" required></textarea>
            </div>
            <div class="mb-3">
                <label class="form-label">Details</label>
                <textarea name="details" class="form-control" rows="4" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary">Add Sponsor</button>
        </form>

        <!-- Display Sponsors -->
        <h2>Sponsors List</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Logo</th>
                    <th>Business Type</th>
                    <th>Contact Email</th>
                    <th>Contact Phone</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $sponsors->fetch_assoc()) { ?>
                <tr>
                    <td><?php echo $row['id']; ?></td>
                    <td><?php echo $row['name']; ?></td>
                    <td><img src="<?php echo $row['logo']; ?>" alt="Logo" width="50"></td>
                    <td><?php echo $row['business_type']; ?></td>
                    <td><?php echo $row['contact_email']; ?></td>
                    <td><?php echo $row['contact_phone']; ?></td>
                    <td>
                        <a href="edit_sponsor.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
                        <a href="delete_sponsor.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</a>
                    </td>
                </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</body>
</html>

<?php include '../includes/footer.php'; ?>