<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';
// Create Team
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_team'])) {
    $name = $_POST['name'];
    $league_id = $_POST['league_id'];
    $description = $_POST['description']; // Get the description

    // Handle logo upload
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $targetDir = "uploads/"; // Directory to store uploaded logos
        $targetFile = $targetDir. basename($_FILES["logo"]["name"]);
        $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
        $uploadOk = 1;

        // Check if image file is a actual image or fake image
        $check = getimagesize($_FILES["logo"]["tmp_name"]);
        if ($check === false) {
            echo "<div class='alert alert-danger'>File is not an image.</div>";
            $uploadOk = 0;
        }

        // Check file size (example: limit to 5MB)
        if ($_FILES["logo"]["size"] > 5000000) {
            echo "<div class='alert alert-danger'>Sorry, your file is too large.</div>";
            $uploadOk = 0;
        }

        // Allow certain file formats
        if ($imageFileType!= "jpg" && $imageFileType!= "png" && $imageFileType!= "jpeg" && $imageFileType!= "gif") {
            echo "<div class='alert alert-danger'>Sorry, only JPG, JPEG, PNG & GIF files are allowed.</div>";
            $uploadOk = 0;
        }

        // Check if $uploadOk is set to 0 by an error
        if ($uploadOk == 0) {
            echo "<div class='alert alert-danger'>Sorry, your file was not uploaded.</div>";
        } else {
            // If everything is ok, try to upload file
            if (move_uploaded_file($_FILES["logo"]["tmp_name"], $targetFile)) {
                $logoName = basename($_FILES["logo"]["name"]); // Get the logo file name
            } else {
                echo "<div class='alert alert-danger'>Sorry, there was an error uploading your file.</div>";
            }
        }
    } else {
        $logoName = ""; // No logo uploaded
    }

    $sql = "INSERT INTO teams (name, league_id, logo, description) 
            VALUES ('$name', '$league_id', '$logoName', '$description')";

    if ($conn->query($sql) === TRUE) {
        echo "<div class='alert alert-success'>Team created successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: ". $sql. "<br>". $conn->error. "</div>";
    }
}

// Fetch Teams
$teams = $conn->query("SELECT teams.*, leagues.name as league_name FROM teams JOIN leagues ON teams.league_id = leagues.id");
$leagues = $conn->query("SELECT * FROM leagues");?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Teams</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
<div class="container mt-4">
    <h1>Manage Teams</h1>
    <form method="POST" class="mb-4" enctype="multipart/form-data">
        <div class="mb-3">
            <input type="text" name="name" placeholder="Team Name" class="form-control" required>
        </div>
        <div class="mb-3">
            <select name="league_id" class="form-control" required>
                <option value="">Select League</option>
                <?php while ($row = $leagues->fetch_assoc()) {?>
                <option value="<?php echo $row['id'];?>"><?php echo $row['name'];?></option>
                <?php }?>
            </select>
        </div>
        <div class="mb-3">
            <label for="logo" class="form-label">Logo</label>
            <input type="file" name="logo" id="logo" class="form-control" accept="image/*" required>
        </div>
        <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea name="description" id="description" class="form-control" rows="5" required></textarea>
        </div>
        <button type="submit" name="create_team" class="btn btn-primary">Create Team</button>
    </form>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>League</th>
                <th>Logo</th>
                <th>Description</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $teams->fetch_assoc()) {?>
            <tr>
                <td><?php echo $row['id'];?></td>
                <td>
                    <a href="team.php?id=<?php echo $row['id'];?>" class="text-primary">
                        <?php echo $row['name'];?>
                    </a>
                </td>
                <td><?php echo $row['league_name'];?></td>
                <td><img src="uploads/<?php echo $row['logo'];?>" alt="Team Logo" width="50"></td>
                <td><?php echo $row['description'];?></td>
                <td>
                    <a href="edit_team.php?id=<?php echo $row['id'];?>" class="btn btn-warning btn-sm">Edit</a>
                    <a href="delete_team.php?id=<?php echo $row['id'];?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</a>
                </td>
            </tr>
            <?php }?>
        </tbody>
    </table>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
<?php include '../includes/footer.php'; ?>
</html>