<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';
if (!isset($_GET['id'])) {
    header("Location: matches.php");
    exit();
}

$id = $_GET['id'];

// Fetch Match Details
$match = $conn->query("SELECT * FROM matches WHERE id = $id")->fetch_assoc();
$leagues = $conn->query("SELECT * FROM leagues");
$teams = $conn->query("SELECT * FROM teams");

// Update Match
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_match'])) {
    $league_id = $_POST['league_id'];
    $home_team_id = $_POST['home_team_id'];
    $away_team_id = $_POST['away_team_id'];
    $date = $_POST['date'];
    $status = $_POST['status'];

    $sql = "UPDATE matches SET league_id = '$league_id', home_team_id = '$home_team_id', away_team_id = '$away_team_id', date = '$date', status = '$status' WHERE id = $id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Match updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}
?>

<h1>Edit Match</h1>
<form method="POST">
    <div class="mb-3">
        <select name="league_id" class="form-control" required>
            <option value="">Select League</option>
            <?php while ($row = $leagues->fetch_assoc()) { ?>
            <option value="<?php echo $row['id']; ?>" <?php if ($row['id'] == $match['league_id']) echo 'selected'; ?>>
                <?php echo $row['name']; ?>
            </option>
            <?php } ?>
        </select>
    </div>
    <div class="mb-3">
        <select name="home_team_id" class="form-control" required>
            <option value="">Select Home Team</option>
            <?php while ($row = $teams->fetch_assoc()) { ?>
            <option value="<?php echo $row['id']; ?>" <?php if ($row['id'] == $match['home_team_id']) echo 'selected'; ?>>
                <?php echo $row['name']; ?>
            </option>
            <?php } ?>
        </select>
    </div>
    <div class="mb-3">
        <select name="away_team_id" class="form-control" required>
            <option value="">Select Away Team</option>
            <?php 
            // Reset the teams result pointer for re-iterating the teams
            $teams->data_seek(0); 
            while ($row = $teams->fetch_assoc()) { ?>
            <option value="<?php echo $row['id']; ?>" <?php if ($row['id'] == $match['away_team_id']) echo 'selected'; ?>>
                <?php echo $row['name']; ?>
            </option>
            <?php } ?>
        </select>
    </div>
    <div class="mb-3">
        <input type="datetime-local" name="date" class="form-control" value="<?php echo date('Y-m-d\TH:i', strtotime($match['date'])); ?>" required>
    </div>
    <div class="mb-3">
        <select name="status" class="form-control" required>
            <option value="upcoming" <?php if ($match['status'] == 'upcoming') echo 'selected'; ?>>Upcoming</option>
            <option value="live" <?php if ($match['status'] == 'live') echo 'selected'; ?>>Live</option>
            <option value="finished" <?php if ($match['status'] == 'finished') echo 'selected'; ?>>Finished</option>
        </select>
    </div>
    <button type="submit" name="update_match" class="btn btn-primary">Update Match</button>
</form>

<?php include '../includes/footer.php'; ?>
