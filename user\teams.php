<?php
include '../includes/db.php';
include '../includes/header.php';

// Create Team
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_team'])) {
    $name = $_POST['name'];
    $league_id = $_POST['league_id'];
    $logo = $_POST['logo'];

    $sql = "INSERT INTO teams (name, league_id, logo) VALUES ('$name', '$league_id', '$logo')";
    if ($conn->query($sql) === TRUE) {
        echo "<div class='alert alert-success'>Team created successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $sql . "<br>" . $conn->error . "</div>";
    }
}

// Fetch Teams
$teams = $conn->query("SELECT teams.*, leagues.name as league_name FROM teams JOIN leagues ON teams.league_id = leagues.id");
$leagues = $conn->query("SELECT * FROM leagues");
?>

<h1>Manage Teams</h1>
<form method="POST" class="mb-4">
    <div class="mb-3">
        <input type="text" name="name" placeholder="Team Name" class="form-control" required>
    </div>
    <div class="mb-3">
        <select name="league_id" class="form-control" required>
            <option value="">Select League</option>
            <?php while ($row = $leagues->fetch_assoc()) { ?>
            <option value="<?php echo $row['id']; ?>"><?php echo $row['name']; ?></option>
            <?php } ?>
        </select>
    </div>
    <div class="mb-3">
        <input type="text" name="logo" placeholder="Logo URL" class="form-control">
    </div>
    <button type="submit" name="create_team" class="btn btn-primary">Create Team</button>
</form>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>ID</th>
            <th>Name</th>
            <th>League</th>
            <th>Logo</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($row = $teams->fetch_assoc()) { ?>
        <tr>
            <td><?php echo $row['id']; ?></td>
            <td>
                <a href="team_details.php?id=<?php echo $row['id']; ?>" class="text-primary">
                    <?php echo $row['name']; ?>
                </a>
            </td>
            <td><?php echo $row['league_name']; ?></td>
            <td><img src="<?php echo $row['logo']; ?>" alt="Team Logo" width="50"></td>
            <td>
                <a href="edit_team.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">Edit</a>
                <a href="delete_team.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</a>
            </td>
        </tr>
        <?php } ?>
    </tbody>
</table>

<?php include '../includes/footer.php'; ?>
