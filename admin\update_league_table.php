<?php
include 'includes/db.php';

$matchId = $_POST['match_id'];
$homeScore = $_POST['home_score'];
$awayScore = $_POST['away_score'];

// Fetch match details
$match = $conn->query("SELECT * FROM matches WHERE id = $matchId")->fetch_assoc();

// Update league table based on match result
if ($homeScore > $awayScore) {
    // Home team wins
    $conn->query("UPDATE league_table SET points = points + 3, played = played + 1, won = won + 1, goals_for = goals_for + $homeScore, goals_against = goals_against + $awayScore WHERE team_id = {$match['home_team_id']}");
} elseif ($homeScore < $awayScore) {
    // Away team wins
    $conn->query("UPDATE league_table SET points = points + 3, played = played + 1, won = won + 1, goals_for = goals_for + $awayScore, goals_against = goals_against + $homeScore WHERE team_id = {$match['away_team_id']}");
} else {
    // Draw
    $conn->query("UPDATE league_table SET points = points + 1, played = played + 1, drawn = drawn + 1, goals_for = goals_for + $homeScore, goals_against = goals_against + $awayScore WHERE team_id = {$match['home_team_id']}");
    $conn->query("UPDATE league_table SET points = points + 1, played = played + 1, drawn = drawn + 1, goals_for = goals_for + $awayScore, goals_against = goals_against + $homeScore WHERE team_id = {$match['away_team_id']}");
}

echo "League table updated!";
?>