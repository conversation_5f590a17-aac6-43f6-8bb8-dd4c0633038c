<?php
include '../includes/db.php';
include '../includes/header.php';

// Fetch League Table
$league_table = $conn->query("SELECT league_table.*, teams.name as team_name 
                              FROM league_table 
                              JOIN teams ON league_table.team_id = teams.id 
                              ORDER BY league_table.points DESC");
?>

<h1>League Table</h1>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Position</th>
            <th>Team</th>
            <th>Played</th>
            <th>Won</th>
            <th>Drawn</th>
            <th>Lost</th>
            <th>GF</th>
            <th>GA</th>
            <th>Points</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $position = 1;
        while ($row = $league_table->fetch_assoc()) { ?>
        <tr>
            <td><?php echo $position++; ?></td>
            <td><?php echo $row['team_name']; ?></td>
            <td><?php echo $row['played']; ?></td>
            <td><?php echo $row['won']; ?></td>
            <td><?php echo $row['drawn']; ?></td>
            <td><?php echo $row['lost']; ?></td>
            <td><?php echo $row['goals_for']; ?></td>
            <td><?php echo $row['goals_against']; ?></td>
            <td><?php echo $row['points']; ?></td>
        </tr>
        <?php } ?>
    </tbody>
</table>

<?php include '../includes/footer.php'; ?>