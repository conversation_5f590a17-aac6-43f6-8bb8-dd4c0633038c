<?php
include '../includes/db.php';
//include '../includes/header.php';
include './includes/header.php';
include 'check_login.php';
if (!isset($_GET['id'])) {
    header("Location: fixtures.php");
    exit();
}

$id = $_GET['id'];

// Fetch Fixture Details
$fixture = $conn->query("SELECT * FROM fixtures WHERE id = $id")->fetch_assoc();
$leagues = $conn->query("SELECT * FROM leagues");
$matches = $conn->query("SELECT matches.*, t1.name as home_team, t2.name as away_team 
                         FROM matches 
                         JOIN teams t1 ON matches.home_team_id = t1.id 
                         JOIN teams t2 ON matches.away_team_id = t2.id");

// Update Fixture
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_fixture'])) {
    $league_id = $_POST['league_id'];
    $match_id = $_POST['match_id'];
    $round = $_POST['round'];

    $sql = "UPDATE fixtures SET league_id = '$league_id', match_id = '$match_id', round = '$round' WHERE id = $id";
    if ($conn->query($sql)) {
        echo "<div class='alert alert-success'>Fixture updated successfully!</div>";
    } else {
        echo "<div class='alert alert-danger'>Error: " . $conn->error . "</div>";
    }
}
?>

<h1>Edit Fixture</h1>
<form method="POST">
    <div class="mb-3">
        <select name="league_id" class="form-control" required>
            <option value="">Select League</option>
            <?php while ($row = $leagues->fetch_assoc()) { ?>
            <option value="<?php echo $row['id']; ?>" <?php if ($row['id'] == $fixture['league_id']) echo 'selected'; ?>><?php echo $row['name']; ?></option>
            <?php } ?>
        </select>
    </div>
    <div class="mb-3">
        <select name="match_id" class="form-control" required>
            <option value="">Select Match</option>
            <?php while ($row = $matches->fetch_assoc()) { ?>
            <option value="<?php echo $row['id']; ?>" <?php if ($row['id'] == $fixture['match_id']) echo 'selected'; ?>><?php echo $row['home_team']; ?> vs <?php echo $row['away_team']; ?></option>
            <?php } ?>
        </select>
    </div>
    <div class="mb-3">
        <input type="number" name="round" placeholder="Round" class="form-control" value="<?php echo $fixture['round']; ?>" required>
    </div>
    <button type="submit" name="update_fixture" class="btn btn-primary">Update Fixture</button>
</form>

<?php include '../includes/footer.php'; ?>